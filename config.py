#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# 网管系统配置
ZTE_CONFIG = {
    "base_url": "https://*************:28001",
    "login_url": "https://*************:28001/uportal/framework/default.html",
    "alarm_url": "https://*************:28001/uportal/framework/default.html#/_ngict-fm-currentAlarm",
    "username": "hzxujx",
    "password": "HZwg@124",
    "login_selectors": {
        "username_input": '#inputUserName',  # 根据页面分析结果
        "password_input": '#inputCiphercode',  # 密码输入框
        "login_button": '#loginBut',  # 登录按钮
        "alarm_menu": 'text="告警管理"',  # 告警管理菜单
        "current_alarm": 'text="当前告警"'  # 当前告警子菜单
    },
    "api_endpoints": {
        "current_alarms": "/api/fm-active/v1/activealarms/table",  # 真正的告警数据API
        "alarm_summary": "/api/fm-active/v1/activealarms/totalandseverity",  # 告警统计API
        "alarm_history": "/api/fm-active/v1/activealarms/history"
    }
}

# 监控配置
MONITOR_CONFIG = {
    "check_interval": 120,  # 检查间隔（秒）- 改为2分钟，更频繁监控
    "time_thresholds": [1, 2, 3, 4, 8, 24, 48, 72],  # 通知时间阈值（小时）
    "non_assessment_hours": (0, 6),  # 非考核时间段（0-6点）
}

# 邮件配置
EMAIL_CONFIG = {
    "smtp_server": "xcs.mail.chinaunicom.cn",  # 联通邮箱SMTP服务器
    "smtp_port": 465,  # SSL端口
    "use_ssl": True,   # 使用SSL加密
    "sender_email": "<EMAIL>",  # 发送方邮箱
    "sender_password": "OAbbd520.",    # 邮箱密码
    "recipients": [
        "<EMAIL>",  # 接收方邮箱
    ]
}

# 特定告警类型配置（需要监控的告警类型）
ALARM_TYPES = {
    "RRU故障": {
        "keywords": ["RRU", "射频单元"],
        "priority": "high"
    },
    "AAU故障": {
        "keywords": ["AAU", "有源天线"],
        "priority": "high"
    },
    "BBU故障": {
        "keywords": ["BBU", "基带单元"],
        "priority": "critical"
    },
    "传输故障": {
        "keywords": ["传输", "光纤", "E1"],
        "priority": "high"
    },
    "电源故障": {
        "keywords": ["电源", "停电", "市电"],
        "priority": "medium"
    }
}

# 兼容新变量名
TARGET_ALARM_TYPES = ALARM_TYPES

# 排除关键词（如果需要的话）
EXCLUDE_KEYWORDS = []

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "file": "alarm_monitor.log",
    "format": "%(asctime)s - %(levelname)s - %(message)s"
}
