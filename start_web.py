#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面启动脚本
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查依赖包"""
    try:
        import flask
        print("✓ Flask 已安装")
        return True
    except ImportError:
        print("✗ Flask 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "flask==3.0.0"])
            print("✓ Flask 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("✗ Flask 安装失败")
            return False

def main():
    """主函数"""
    print("=" * 50)
    print("菏泽联通告警监控系统 - Web界面")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败，请手动安装 Flask")
        return
    
    # 检查配置文件
    if not os.path.exists('config.py'):
        print("✗ 配置文件 config.py 不存在")
        return
    
    if not os.path.exists('alarm_monitor.py'):
        print("✗ 主程序文件 alarm_monitor.py 不存在")
        return
    
    print("✓ 配置文件检查通过")
    print("✓ 主程序文件检查通过")
    
    # 启动Web服务
    print("\n启动Web界面...")
    print("访问地址: http://localhost:8080")
    print("按 Ctrl+C 停止服务\n")

    try:
        from web_dashboard import app
        app.run(host='0.0.0.0', port=8080, debug=False)
    except KeyboardInterrupt:
        print("\n\nWeb服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
