{% extends "base.html" %}

{% block title %}组告警管理 - 告警监控系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-collection"></i> 组告警管理</h2>
    <div>
        <button class="btn btn-outline-primary me-2" onclick="refreshGroups()">
            <i class="bi bi-arrow-clockwise"></i> 刷新数据
        </button>
        <button class="btn btn-outline-danger" onclick="clearHistory()">
            <i class="bi bi-trash"></i> 清除历史
        </button>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <div class="metric-value text-primary" id="total-groups">0</div>
                <div class="metric-label">总组数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <div class="metric-value text-warning" id="active-groups">0</div>
                <div class="metric-label">活跃组数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <div class="metric-value text-success" id="notified-groups">0</div>
                <div class="metric-label">已通知组数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <div class="metric-value text-info" id="max-threshold">0</div>
                <div class="metric-label">最高阈值(小时)</div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <input type="text" class="form-control" id="search-input" placeholder="搜索组ID或站点名称..." onkeyup="filterGroups()">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="threshold-filter" onchange="filterGroups()">
                    <option value="">所有阈值</option>
                    <option value="1">1小时</option>
                    <option value="2">2小时</option>
                    <option value="3">3小时</option>
                    <option value="4">4小时</option>
                    <option value="8">8小时</option>
                    <option value="24">24小时</option>
                    <option value="48">48小时</option>
                    <option value="72">72小时</option>
                </select>
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                    <i class="bi bi-x-circle"></i> 重置过滤
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 组告警列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 组告警历史记录</h5>
    </div>
    <div class="card-body">
        <div id="groups-container">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载组告警数据...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let allGroups = {};
let filteredGroups = {};

// 加载组数据
async function loadGroups() {
    const data = await apiCall('/api/groups');
    if (data) {
        allGroups = data;
        filteredGroups = { ...allGroups };
        updateGroupsDisplay();
        updateStatistics();
    }
}

// 更新统计信息
function updateStatistics() {
    const totalGroups = Object.keys(allGroups).length;
    const activeGroups = Object.values(allGroups).filter(thresholds => thresholds.length > 0).length;
    const notifiedGroups = Object.values(allGroups).filter(thresholds => thresholds.length > 0).length;
    const maxThreshold = Math.max(...Object.values(allGroups).flat(), 0);
    
    document.getElementById('total-groups').textContent = totalGroups;
    document.getElementById('active-groups').textContent = activeGroups;
    document.getElementById('notified-groups').textContent = notifiedGroups;
    document.getElementById('max-threshold').textContent = maxThreshold;
}

// 更新组显示
function updateGroupsDisplay() {
    const container = document.getElementById('groups-container');
    const groupEntries = Object.entries(filteredGroups);
    
    if (groupEntries.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">没有找到匹配的组告警记录</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += `
        <thead class="table-light">
            <tr>
                <th>组ID</th>
                <th>已触发阈值</th>
                <th>最高阈值</th>
                <th>阈值数量</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
    `;
    
    groupEntries.forEach(([groupId, thresholds]) => {
        const maxThreshold = Math.max(...thresholds, 0);
        const thresholdCount = thresholds.length;
        const thresholdBadges = thresholds.sort((a, b) => a - b).map(t => 
            `<span class="badge bg-${getThresholdColor(t)} me-1">${t}h</span>`
        ).join('');
        
        html += `
            <tr>
                <td>
                    <code class="text-primary">${groupId.substring(0, 20)}...</code>
                </td>
                <td>${thresholdBadges || '<span class="text-muted">无</span>'}</td>
                <td>
                    <span class="badge bg-${getThresholdColor(maxThreshold)}">${maxThreshold}小时</span>
                </td>
                <td>${thresholdCount}</td>
                <td>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeGroup('${groupId}')">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// 获取阈值颜色
function getThresholdColor(threshold) {
    if (threshold >= 72) return 'danger';
    if (threshold >= 24) return 'warning';
    if (threshold >= 8) return 'info';
    if (threshold >= 4) return 'primary';
    return 'secondary';
}

// 过滤组
function filterGroups() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    const thresholdFilter = document.getElementById('threshold-filter').value;
    
    filteredGroups = {};
    
    Object.entries(allGroups).forEach(([groupId, thresholds]) => {
        let matchesSearch = true;
        let matchesThreshold = true;
        
        // 搜索过滤
        if (searchTerm) {
            matchesSearch = groupId.toLowerCase().includes(searchTerm);
        }
        
        // 阈值过滤
        if (thresholdFilter) {
            matchesThreshold = thresholds.includes(parseInt(thresholdFilter));
        }
        
        if (matchesSearch && matchesThreshold) {
            filteredGroups[groupId] = thresholds;
        }
    });
    
    updateGroupsDisplay();
}

// 重置过滤器
function resetFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('threshold-filter').value = '';
    filteredGroups = { ...allGroups };
    updateGroupsDisplay();
}

// 刷新组数据
async function refreshGroups() {
    showAlert('正在刷新数据...', 'info');
    await loadGroups();
    showAlert('数据已刷新', 'success');
}

// 删除单个组
async function removeGroup(groupId) {
    if (!confirm('确定要删除这个组的历史记录吗？')) {
        return;
    }
    
    // 这里可以添加删除单个组的API
    showAlert('单个组删除功能待实现', 'warning');
}

// 清除所有历史
async function clearHistory() {
    if (!confirm('确定要清除所有组告警历史记录吗？此操作不可恢复！')) {
        return;
    }
    
    const result = await apiCall('/api/clear_history', 'POST');
    if (result && result.success) {
        showAlert(result.message, 'success');
        allGroups = {};
        filteredGroups = {};
        updateGroupsDisplay();
        updateStatistics();
    } else {
        showAlert(result ? result.message : '清除失败', 'danger');
    }
}

// 页面加载完成后加载数据
document.addEventListener('DOMContentLoaded', function() {
    loadGroups();
});
</script>
{% endblock %}
