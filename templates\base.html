<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}告警监控系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .status-running { color: #28a745; }
        .status-stopped { color: #dc3545; }
        .status-starting { color: #ffc107; }
        .card-metric { text-align: center; padding: 1rem; }
        .metric-value { font-size: 2rem; font-weight: bold; }
        .metric-label { color: #6c757d; font-size: 0.9rem; }
        .log-container {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        .navbar-brand { font-weight: bold; }
        .alert-dismissible { margin-bottom: 1rem; }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
            margin-bottom: 1.5rem;
        }
        .card-header {
            background-color: #ffffff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
        /* 全宽布局优化 */
        .container-fluid {
            max-width: none;
        }
        /* 响应式调整 */
        @media (max-width: 768px) {
            .container-fluid {
                padding-left: 15px;
                padding-right: 15px;
            }
        }
        /* 表格响应式 */
        .table-responsive {
            border-radius: 0.375rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid px-4">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-shield-check"></i> 菏泽联通告警监控系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="bi bi-speedometer2"></i> 监控概览
                </a>
                <a class="nav-link" href="{{ url_for('groups') }}">
                    <i class="bi bi-collection"></i> 组告警管理
                </a>
                <a class="nav-link" href="{{ url_for('config') }}">
                    <i class="bi bi-gear"></i> 系统配置
                </a>
                <a class="nav-link" href="{{ url_for('logs') }}">
                    <i class="bi bi-file-text"></i> 系统日志
                </a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid px-4 mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 通用函数
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // API调用函数
        async function apiCall(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                console.error('API调用失败:', error);
                showAlert('网络请求失败', 'danger');
                return null;
            }
        }
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
