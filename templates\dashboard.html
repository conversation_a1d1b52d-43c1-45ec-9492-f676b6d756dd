{% extends "base.html" %}

{% block title %}监控概览 - 告警监控系统{% endblock %}

{% block content %}
<!-- 系统状态和控制 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-activity"></i> 系统状态</h5>
                <span id="status-badge" class="badge bg-secondary">加载中...</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>运行状态:</strong> <span id="system-status">加载中...</span></p>
                        <p><strong>最后检查:</strong> <span id="last-check">-</span></p>
                    </div>
                    <div class="col-md-6">
                        <button id="start-btn" class="btn btn-success me-2" onclick="startMonitor()">
                            <i class="bi bi-play-fill"></i> 启动监控
                        </button>
                        <button id="stop-btn" class="btn btn-danger" onclick="stopMonitor()">
                            <i class="bi bi-stop-fill"></i> 停止监控
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-bell"></i> 今日通知</h5>
            </div>
            <div class="card-body card-metric">
                <div class="metric-value text-primary" id="notifications-sent">0</div>
                <div class="metric-label">已发送组通知</div>
            </div>
        </div>
    </div>
</div>

<!-- 监控指标 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body card-metric">
                <div class="metric-value text-info" id="total-alarms">0</div>
                <div class="metric-label">总告警数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body card-metric">
                <div class="metric-value text-warning" id="target-alarms">0</div>
                <div class="metric-label">目标告警数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body card-metric">
                <div class="metric-value text-success" id="groups-count">0</div>
                <div class="metric-label">告警组数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body card-metric">
                <div class="metric-value text-secondary" id="groups-in-history">0</div>
                <div class="metric-label">历史组数</div>
            </div>
        </div>
    </div>
</div>

<!-- 告警列表和统计 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-exclamation-triangle"></i> 目标告警列表</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary me-2" onclick="refreshAlarms()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleAutoRefresh()">
                        <i class="bi bi-play-circle" id="auto-refresh-icon"></i>
                        <span id="auto-refresh-text">自动刷新</span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 快速过滤 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control form-control-sm" id="search-input" placeholder="搜索站点或告警内容..." onkeyup="searchAlarms()">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select form-select-sm" id="role-filter" onchange="filterAlarms()">
                            <option value="">所有角色</option>
                            <option value="根因">根因</option>
                            <option value="次根因">次根因</option>
                            <option value="衍生">衍生</option>
                            <option value="独立">独立</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select form-select-sm" id="sort-field" onchange="sortAlarms()">
                            <option value="time">按时间</option>
                            <option value="station">按站点</option>
                            <option value="role">按角色</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select form-select-sm" id="per-page" onchange="changePageSize()">
                            <option value="20">显示20条</option>
                            <option value="50" selected>显示50条</option>
                            <option value="100">显示100条</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary btn-sm w-100" onclick="resetFilters()">
                            <i class="bi bi-x-circle"></i> 重置
                        </button>
                    </div>
                </div>

                <!-- 目标告警表格 -->
                <div class="table-responsive" style="max-height: 500px;">
                    <table class="table table-hover table-striped table-sm mb-0" id="alarms-table">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th style="width: 130px;">发生时间</th>
                                <th style="width: 250px;">基站名称</th>
                                <th style="width: 220px;">告警内容</th>
                                <th style="width: 180px;">告警对象名称</th>
                                <th style="width: 80px;">告警角色</th>
                                <th style="width: 80px;">清除状态</th>
                                <th style="width: 80px;">确认状态</th>
                                <th style="width: 90px;">持续时间</th>
                            </tr>
                        </thead>
                        <tbody id="alarms-tbody">
                            <tr>
                                <td colspan="8" class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <span class="ms-2">正在加载告警数据...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页信息 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <small class="text-muted">
                        显示第 <span id="start-item">0</span> - <span id="end-item">0</span> 条，共 <span id="total-items">0</span> 条目标告警
                        | 根因: <span id="root-count" class="text-danger">0</span>
                        | 次根因: <span id="secondary-count" class="text-warning">0</span>
                        | 衍生: <span id="derived-count" class="text-info">0</span>
                        | 独立: <span id="independent-count" class="text-success">0</span>
                    </small>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="pagination">
                            <!-- 分页按钮 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最新日志 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-terminal"></i> 最新日志</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshLogs()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <div id="system-logs" class="log-container">
                    <p class="text-muted">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let refreshInterval;

// 启动监控
async function startMonitor() {
    const result = await apiCall('/api/start', 'POST');
    if (result && result.success) {
        showAlert(result.message, 'success');
        updateStatus();
    } else {
        showAlert(result ? result.message : '启动失败', 'danger');
    }
}

// 停止监控
async function stopMonitor() {
    const result = await apiCall('/api/stop', 'POST');
    if (result && result.success) {
        showAlert(result.message, 'warning');
        updateStatus();
    } else {
        showAlert(result ? result.message : '停止失败', 'danger');
    }
}

// 更新状态
async function updateStatus() {
    const data = await apiCall('/api/status');
    if (!data) return;
    
    // 更新状态显示
    document.getElementById('system-status').textContent = data.status;
    document.getElementById('last-check').textContent = data.last_check || '-';
    document.getElementById('total-alarms').textContent = data.total_alarms || 0;
    document.getElementById('target-alarms').textContent = data.target_alarms || 0;
    document.getElementById('groups-count').textContent = data.groups_count || 0;
    document.getElementById('notifications-sent').textContent = data.notifications_sent || 0;
    document.getElementById('groups-in-history').textContent = data.groups_in_history || 0;
    
    // 更新状态徽章
    const statusBadge = document.getElementById('status-badge');
    const startBtn = document.getElementById('start-btn');
    const stopBtn = document.getElementById('stop-btn');
    
    if (data.monitor_running) {
        statusBadge.className = 'badge bg-success';
        statusBadge.textContent = '运行中';
        startBtn.disabled = true;
        stopBtn.disabled = false;
    } else {
        statusBadge.className = 'badge bg-danger';
        statusBadge.textContent = '已停止';
        startBtn.disabled = false;
        stopBtn.disabled = true;
    }
    
    // 更新告警表格
    if (typeof loadAlarms === 'function') {
        loadAlarms(currentPage);
    }

    // 更新日志
    updateLogs(data.system_logs || []);
}

// 告警表格相关变量
let currentPage = 1;
let totalPages = 1;
let autoRefreshInterval = null;
let autoRefreshEnabled = false;

// 加载告警数据
async function loadAlarms(page = 1) {
    const search = document.getElementById('search-input').value;
    const role = document.getElementById('role-filter').value;
    const sort = document.getElementById('sort-field').value;
    const perPage = document.getElementById('per-page').value;

    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        role: role,
        sort: sort,
        order: 'desc'
    });

    try {
        const response = await fetch(`/api/alarms?${params}`);
        const data = await response.json();

        if (data.error) {
            console.error('加载告警失败:', data.error);
            return;
        }

        currentPage = data.page;
        totalPages = data.pages;

        updateAlarmsTable(data.items);
        updateAlarmStatistics(data.items, data.total);
        updateAlarmPagination(data);

    } catch (error) {
        console.error('加载告警数据失败:', error);
    }
}

// 更新告警表格
function updateAlarmsTable(alarms) {
    const tbody = document.getElementById('alarms-tbody');

    if (alarms.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-3">
                    <i class="bi bi-inbox text-muted"></i>
                    <span class="ms-2 text-muted">暂无告警数据</span>
                </td>
            </tr>
        `;
        return;
    }

    let html = '';
    alarms.forEach(alarm => {
        const roleClass = getRoleClass(alarm.role);
        const roleBadge = `<span class="badge ${roleClass} badge-sm">${alarm.role}</span>`;

        // 格式化时间显示
        const timeStr = alarm.time ? alarm.time.substring(5, 16) : ''; // 只显示月-日 时:分

        html += `
            <tr>
                <td><small>${timeStr}</small></td>
                <td>
                    <div class="text-truncate" style="max-width: 230px;" title="${alarm.station}">
                        <small>${alarm.station}</small>
                    </div>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;" title="${alarm.content}">
                        <small>${alarm.content}</small>
                    </div>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 160px;" title="${alarm.object}">
                        <small>${alarm.object}</small>
                    </div>
                </td>
                <td>${roleBadge}</td>
                <td><small class="text-${alarm.clear_status === '已清除' ? 'success' : 'danger'}">${alarm.clear_status}</small></td>
                <td><small class="text-${alarm.ack_status === '已确认' ? 'success' : 'warning'}">${alarm.ack_status}</small></td>
                <td><small>${alarm.duration}</small></td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

// 获取角色样式类
function getRoleClass(role) {
    switch(role) {
        case '根因': return 'bg-danger';
        case '次根因': return 'bg-warning text-dark';
        case '衍生': return 'bg-info';
        case '独立': return 'bg-success';
        default: return 'bg-secondary';
    }
}

// 更新告警统计
function updateAlarmStatistics(alarms, total) {
    const roleCounts = {
        '根因': 0,
        '次根因': 0,
        '衍生': 0,
        '独立': 0
    };

    alarms.forEach(alarm => {
        if (roleCounts.hasOwnProperty(alarm.role)) {
            roleCounts[alarm.role]++;
        }
    });

    // 更新表格下方的统计
    document.getElementById('root-count').textContent = roleCounts['根因'];
    document.getElementById('secondary-count').textContent = roleCounts['次根因'];
    document.getElementById('derived-count').textContent = roleCounts['衍生'];
    document.getElementById('independent-count').textContent = roleCounts['独立'];

    // 同时更新顶部的目标告警数
    document.getElementById('target-alarms').textContent = total;
}

// 更新分页
function updateAlarmPagination(data) {
    const pagination = document.getElementById('pagination');
    const startItem = (data.page - 1) * data.per_page + 1;
    const endItem = Math.min(data.page * data.per_page, data.total);

    document.getElementById('start-item').textContent = data.total > 0 ? startItem : 0;
    document.getElementById('end-item').textContent = endItem;
    document.getElementById('total-items').textContent = data.total;

    let html = '';

    if (data.pages > 1) {
        // 上一页
        html += `
            <li class="page-item ${data.page <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadAlarms(${data.page - 1})">‹</a>
            </li>
        `;

        // 页码（最多显示5页）
        const maxPages = 5;
        let startPage = Math.max(1, data.page - Math.floor(maxPages / 2));
        let endPage = Math.min(data.pages, startPage + maxPages - 1);

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === data.page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="loadAlarms(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        html += `
            <li class="page-item ${data.page >= data.pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadAlarms(${data.page + 1})">›</a>
            </li>
        `;
    }

    pagination.innerHTML = html;
}

// 告警表格操作函数
function searchAlarms() {
    loadAlarms(1);
}

function filterAlarms() {
    loadAlarms(1);
}

function sortAlarms() {
    loadAlarms(currentPage);
}

function changePageSize() {
    loadAlarms(1);
}

function resetFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('role-filter').value = '';
    document.getElementById('sort-field').value = 'time';
    loadAlarms(1);
}

function refreshAlarms() {
    loadAlarms(currentPage);
}

function toggleAutoRefresh() {
    const icon = document.getElementById('auto-refresh-icon');
    const text = document.getElementById('auto-refresh-text');

    if (autoRefreshEnabled) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        autoRefreshEnabled = false;
        icon.className = 'bi bi-play-circle';
        text.textContent = '自动刷新';
    } else {
        autoRefreshInterval = setInterval(() => {
            loadAlarms(currentPage);
        }, 10000); // 每10秒刷新一次
        autoRefreshEnabled = true;
        icon.className = 'bi bi-stop-circle';
        text.textContent = '停止刷新';
    }
}

// 更新日志显示
function updateLogs(logs) {
    const container = document.getElementById('system-logs');
    if (logs.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无日志</p>';
        return;
    }
    
    const html = logs.slice(-20).map(log => `<div>${log}</div>`).join('');
    container.innerHTML = html;
    container.scrollTop = container.scrollHeight;
}

// 刷新日志
async function refreshLogs() {
    const data = await apiCall('/api/logs');
    if (data && data.logs) {
        updateLogs(data.logs);
    }
}

// 页面加载完成后开始定时刷新
document.addEventListener('DOMContentLoaded', function() {
    updateStatus();
    loadAlarms(); // 初始加载告警数据
    refreshInterval = setInterval(updateStatus, 5000); // 每5秒刷新一次
});

// 页面卸载时清除定时器
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>

<style>
.table-sm th,
.table-sm td {
    padding: 0.4rem 0.5rem;
    font-size: 0.85rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #343a40 !important;
    color: white !important;
}

.table-responsive {
    border-radius: 0.375rem;
}

.sticky-top {
    position: sticky;
    top: 0;
    z-index: 10;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.badge-sm {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
}

.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.form-control-sm,
.form-select-sm {
    font-size: 0.875rem;
}

/* 表格行悬停效果 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 状态颜色 */
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }
</style>
{% endblock %}
