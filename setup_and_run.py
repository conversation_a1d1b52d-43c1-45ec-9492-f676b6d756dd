#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置和运行告警监控系统
"""

import os
import sys
import subprocess
import asyncio
from config import ZTE_CONFIG, EMAIL_CONFIG

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖包安装失败: {e}")
        return False

def install_playwright():
    """安装Playwright浏览器"""
    print("正在安装Playwright浏览器...")
    try:
        subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
        print("Playwright浏览器安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Playwright浏览器安装失败: {e}")
        return False

def check_config():
    """检查配置是否完整"""
    print("检查配置...")
    
    issues = []
    
    # 检查网管配置
    if not ZTE_CONFIG["base_url"] or ZTE_CONFIG["base_url"] == "http://your-zte-system-url":
        issues.append("请在config.py中设置正确的网管系统地址 (ZTE_CONFIG['base_url'])")
    
    # 检查邮件配置
    if not EMAIL_CONFIG["sender_email"] or EMAIL_CONFIG["sender_email"] == "<EMAIL>":
        issues.append("请在config.py中设置发送方邮箱 (EMAIL_CONFIG['sender_email'])")
    
    if not EMAIL_CONFIG["sender_password"] or EMAIL_CONFIG["sender_password"] == "your_password":
        issues.append("请在config.py中设置邮箱密码 (EMAIL_CONFIG['sender_password'])")
    
    if not EMAIL_CONFIG["recipients"] or EMAIL_CONFIG["recipients"] == ["<EMAIL>", "<EMAIL>"]:
        issues.append("请在config.py中设置接收方邮箱列表 (EMAIL_CONFIG['recipients'])")
    
    if issues:
        print("配置检查发现问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    
    print("配置检查通过")
    return True

def print_usage():
    """打印使用说明"""
    print("""
=== 中兴网管告警监控系统 ===

网管地址: https://*************:28001/uportal/framework/default.html#/_ngict-fm-currentAlarm
用户名: hzxujx
发送邮箱: <EMAIL>
接收邮箱: <EMAIL>

运行方式：
python setup_and_run.py [选项]

选项：
  install    - 安装依赖包
  config     - 检查配置
  password   - 密码管理
  debug      - 调试网管连接
  run        - 运行监控程序
  help       - 显示此帮助信息

示例：
python setup_and_run.py install   # 首次运行前安装依赖
python setup_and_run.py config    # 检查配置
python setup_and_run.py password  # 管理密码
python setup_and_run.py debug     # 调试连接
python setup_and_run.py run       # 开始监控

功能说明：
- 自动登录中兴网管系统
- 监控特定告警类型（RRU、AAU、BBU等故障）
- 排除0-6点非考核时间，准确计算持续时间
- 按1、2、3、4、8、24、48、72小时阈值发送邮件通知
- 防止重复通知同一告警的同一时间阈值
    """)

async def run_monitor():
    """运行监控程序"""
    from alarm_monitor import ZTEAlarmMonitor
    
    monitor = ZTEAlarmMonitor()
    
    print("正在启动告警监控系统...")
    print(f"检查间隔: {monitor.check_interval}秒")
    print(f"时间阈值: {monitor.time_thresholds}小时")
    print(f"非考核时间: {monitor.non_assessment_hours[0]}:00-{monitor.non_assessment_hours[1]}:00")
    
    # 登录获取cookies
    if await monitor.login_with_playwright():
        print("登录成功，开始监控...")
        # 开始监控
        await monitor.monitor_alarms()
    else:
        print("登录失败，程序退出")
        return False

def run_password_manager():
    """运行密码管理工具"""
    try:
        from password_manager import main as password_main
        password_main()
    except ImportError:
        print("密码管理模块未找到，请确保password_manager.py存在")
    except Exception as e:
        print(f"密码管理工具运行出错: {e}")

def run_debug_tool():
    """运行调试工具"""
    try:
        import asyncio
        from debug_connection import main as debug_main
        asyncio.run(debug_main())
    except ImportError:
        print("调试模块未找到，请确保debug_connection.py存在")
    except Exception as e:
        print(f"调试工具运行出错: {e}")

def main():
    if len(sys.argv) < 2:
        print_usage()
        return

    command = sys.argv[1].lower()

    if command == "install":
        success = install_dependencies()
        if success:
            success = install_playwright()
        if success:
            print("\n安装完成！")
            print("下一步：")
            print("1. python setup_and_run.py config   # 检查配置")
            print("2. python setup_and_run.py password # 管理密码（可选）")
            print("3. python setup_and_run.py debug    # 调试连接（可选）")
            print("4. python setup_and_run.py run      # 开始监控")

    elif command == "config":
        if check_config():
            print("\n配置正确！可以运行: python setup_and_run.py run")
        else:
            print("\n请修改config.py中的配置后重新检查")

    elif command == "password":
        run_password_manager()

    elif command == "debug":
        run_debug_tool()

    elif command == "run":
        if not check_config():
            print("配置检查失败，请先修改config.py")
            return

        try:
            asyncio.run(run_monitor())
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行出错: {e}")

    elif command == "help":
        print_usage()

    else:
        print(f"未知命令: {command}")
        print_usage()

if __name__ == "__main__":
    main()
