{% extends "base.html" %}

{% block title %}系统配置 - 告警监控系统{% endblock %}

{% block content %}
<h2><i class="bi bi-gear"></i> 系统配置</h2>
<p class="text-muted">查看当前系统配置信息（只读模式）</p>

<!-- 邮件配置 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-envelope"></i> 邮件配置</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">SMTP服务器</label>
                    <input type="text" class="form-control" value="{{ email_config.smtp_server }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">SMTP端口</label>
                    <input type="text" class="form-control" value="{{ email_config.smtp_port }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">发送邮箱</label>
                    <input type="text" class="form-control" value="{{ email_config.smtp_user }}" readonly>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">使用SSL</label>
                    <input type="text" class="form-control" value="{{ '是' if email_config.use_ssl else '否' }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">邮件密码</label>
                    <input type="password" class="form-control" value="********" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">收件人列表</label>
                    <textarea class="form-control" rows="3" readonly>{{ email_config.recipients | join('\n') }}</textarea>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 监控配置 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-activity"></i> 监控配置</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">网管地址</label>
                    <input type="text" class="form-control" value="{{ monitor_config.base_url }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-control" value="{{ monitor_config.username }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">检查间隔(秒)</label>
                    <input type="text" class="form-control" value="{{ monitor_config.check_interval }}" readonly>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">登录密码</label>
                    <input type="password" class="form-control" value="********" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">时间阈值(小时)</label>
                    <input type="text" class="form-control" value="{{ monitor_config.time_thresholds | join(', ') }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">非考核时间</label>
                    <input type="text" class="form-control" value="{{ monitor_config.non_assessment_hours }}" readonly>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 目标告警配置 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-bullseye"></i> 目标告警配置</h5>
        <button class="btn btn-sm btn-outline-info" onclick="loadCurrentAlarmTypes()">
            <i class="bi bi-eye"></i> 查看当前配置
        </button>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">监控的告警类型</label>
                    <div class="form-control" style="height: auto; min-height: 150px; overflow-y: auto;">
                        {% if target_alarm_types %}
                            {% for type_name, config in target_alarm_types.items() %}
                            <div class="mb-2">
                                <span class="badge bg-primary me-2">{{ type_name }}</span>
                                <small class="text-muted">{{ config.keywords | join(', ') }}</small>
                            </div>
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">未配置</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">排除的告警关键词</label>
                    <div class="form-control" style="height: auto; min-height: 150px; overflow-y: auto;">
                        {% if exclude_keywords %}
                            {% for exclude in exclude_keywords %}
                            <span class="badge bg-secondary me-1 mb-1">{{ exclude }}</span>
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">未配置</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 原始数据结构 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-database"></i> 原始数据结构</h5>
        <button class="btn btn-sm btn-outline-primary" onclick="loadRawDataSample()">
            <i class="bi bi-arrow-clockwise"></i> 查看样例
        </button>
    </div>
    <div class="card-body">
        <div id="raw-data-info">
            <p class="text-muted">点击"查看样例"按钮获取当前告警的原始数据结构</p>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-info-circle"></i> 系统信息</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>系统版本:</strong> 菏泽联通告警监控系统 v1.0</p>
                <p><strong>部署时间:</strong> <span id="deploy-time">-</span></p>
                <p><strong>配置文件:</strong> config.py</p>
            </div>
            <div class="col-md-6">
                <p><strong>Web界面端口:</strong> 8080</p>
                <p><strong>日志文件:</strong> alarm_monitor.log</p>
                <p><strong>历史记录:</strong> group_history.json</p>
            </div>
        </div>
        
        <div class="alert alert-info mt-3">
            <i class="bi bi-info-circle"></i>
            <strong>配置修改说明:</strong> 
            如需修改配置，请直接编辑 <code>config.py</code> 文件，然后重启监控系统。
            Web界面目前只提供查看功能，不支持在线修改配置。
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 设置部署时间
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('deploy-time').textContent = new Date().toLocaleString('zh-CN');
});

// 加载原始数据样例
async function loadRawDataSample() {
    const container = document.getElementById('raw-data-info');
    container.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> 正在获取数据...</div>';

    try {
        const response = await fetch('/api/raw_data_sample');
        const data = await response.json();

        if (data.success) {
            let html = `
                <div class="alert alert-info">
                    <strong>数据概览:</strong> 共分析了 ${data.total_samples} 条告警样例，发现 ${data.total_unique_fields} 个不同的字段
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>所有字段列表 (${data.total_unique_fields}个):</h6>
                        <div class="form-control" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.8rem;">
            `;

            data.all_fields.forEach(field => {
                html += `<div>${field}</div>`;
            });

            html += `
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>关键字段数据:</h6>
                        <div class="form-control" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.8rem;">
            `;

            Object.entries(data.key_fields_data).forEach(([key, info]) => {
                if (info.type === 'dict') {
                    html += `<div class="mb-2">
                        <strong class="text-primary">${key}:</strong> (字典类型)
                        <div class="ms-3">
                            <div>keys: [${info.keys.join(', ')}]</div>
                            <div>value: "${info.value}"</div>
                            <div>displayname: "${info.displayname}"</div>
                        </div>
                    </div>`;
                } else {
                    html += `<div class="mb-1">
                        <strong class="text-primary">${key}:</strong> (${info.type}) ${info.value}
                    </div>`;
                }
            });

            html += `
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <h6>样例告警字段统计:</h6>
                    <div class="row">
            `;

            data.samples.forEach(sample => {
                html += `
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">样例 ${sample.index}</h6>
                                <p class="card-text">字段数量: ${sample.field_count}</p>
                                <small class="text-muted">前10个字段: ${sample.sample_fields.join(', ')}</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <strong>说明:</strong> 这些是从当前告警数据中提取的原始字段。关键字段数据显示了Web界面主要使用的字段及其数据结构。
                        字典类型的字段通常包含 value 和 displayname 两个子字段。
                    </small>
                </div>
            `;

            container.innerHTML = html;
        } else {
            container.innerHTML = `<div class="alert alert-warning">${data.message}</div>`;
        }
    } catch (error) {
        container.innerHTML = `<div class="alert alert-danger">获取数据失败: ${error.message}</div>`;
    }
}

// 加载当前ALARM_TYPES配置
async function loadCurrentAlarmTypes() {
    try {
        const response = await fetch('/api/current_alarm_types');
        const data = await response.json();

        if (data.success) {
            let html = `
                <div class="alert alert-success">
                    <strong>当前配置概览:</strong> 共 ${data.total_types} 种告警类型，包含 ${data.total_keywords} 个关键词
                </div>
                <div class="row">
            `;

            Object.entries(data.alarm_types).forEach(([typeName, config]) => {
                const keywords = config.keywords || [];
                const priority = config.priority || 'unknown';
                const priorityClass = {
                    'critical': 'danger',
                    'high': 'warning',
                    'medium': 'info',
                    'low': 'secondary'
                }[priority] || 'secondary';

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">${typeName}</h6>
                                <span class="badge bg-${priorityClass}">${priority}</span>
                            </div>
                            <div class="card-body">
                                <p class="mb-2"><strong>关键词 (${keywords.length}个):</strong></p>
                                <div class="keywords-container">
                `;

                keywords.forEach(keyword => {
                    html += `<span class="badge bg-light text-dark me-1 mb-1">${keyword}</span>`;
                });

                html += `
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                </div>
                <div class="mt-3">
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> 配置说明:</h6>
                        <ul class="mb-0">
                            <li><strong>修改方式:</strong> 直接编辑 <code>config.py</code> 文件中的 <code>ALARM_TYPES</code> 配置</li>
                            <li><strong>生效方式:</strong> 修改后重启监控程序即可生效</li>
                            <li><strong>匹配逻辑:</strong> 告警内容包含任一关键词即被识别为目标告警</li>
                            <li><strong>优先级:</strong> critical > high > medium > low</li>
                        </ul>
                    </div>
                </div>
            `;

            // 创建模态框显示配置
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">当前 ALARM_TYPES 配置</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${html}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // 模态框关闭后移除DOM
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });

        } else {
            alert(`获取配置失败: ${data.error}`);
        }
    } catch (error) {
        alert(`网络请求失败: ${error.message}`);
    }
}
</script>

<style>
.keywords-container {
    max-height: 120px;
    overflow-y: auto;
}
</style>
{% endblock %}
