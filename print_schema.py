#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打印告警数据的所有字段属性和样例值
分析同一站点下的字段差异
"""

import json
import os
from collections import defaultdict

def flatten_keys(d, prefix="", depth=3):
    """返回所有 key（含嵌套 dict 的 key 路径），depth 控嵌套展开层数"""
    keys = set()
    if isinstance(d, dict):
        for k, v in d.items():
            k2 = f"{prefix}.{k}" if prefix else k
            keys.add(k2)
            if depth > 0 and isinstance(v, dict):
                keys |= flatten_keys(v, k2, depth - 1)
    return keys

def flatten(d, prefix="", depth=3, out=None):
    """把 dict 展平成 {路径key: value} 的形式，便于对比"""
    if out is None:
        out = {}
    if isinstance(d, dict):
        for k, v in d.items():
            k2 = f"{prefix}.{k}" if prefix else k
            if depth > 0 and isinstance(v, dict):
                flatten(v, k2, depth - 1, out)
            else:
                out[k2] = v
    else:
        out[prefix] = d
    return out

def find_json_file():
    """查找可用的告警数据文件"""
    candidates = [
        "real_request_all_alarms.json",
        "all_alarms_paginated.json",
        "page_1_data.json",
        "query_test_1.json",
        "captured_requests.json"
    ]

    for filename in candidates:
        if os.path.exists(filename):
            return filename

    print("未找到告警数据文件，正在尝试获取数据...")
    return None

async def fetch_alarm_data():
    """直接从监控系统获取告警数据"""
    try:
        from alarm_monitor import ZTEAlarmMonitor

        print("正在启动监控系统获取告警数据...")
        monitor = ZTEAlarmMonitor()

        # 登录
        if await monitor.login_with_playwright():
            print("登录成功，正在获取告警数据...")

            # 获取原始告警数据
            raw_alarms = await monitor.fetch_all_current_alarms(pagesize=500, max_alarms=1000)

            if raw_alarms:
                # 保存数据到文件
                filename = "fetched_alarms.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(raw_alarms, f, indent=2, ensure_ascii=False)
                print(f"已保存 {len(raw_alarms)} 条告警数据到 {filename}")

                # 清理资源
                await monitor.cleanup()
                return filename
            else:
                print("未能获取到告警数据")
                await monitor.cleanup()
                return None
        else:
            print("登录失败")
            return None

    except Exception as e:
        print(f"获取数据失败: {e}")
        return None

async def main():
    json_file = find_json_file()
    if not json_file:
        # 尝试直接获取数据
        json_file = await fetch_alarm_data()
        if not json_file:
            return
    
    print(f"正在分析文件: {json_file}")
    
    with open(json_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    
    # 如果是完整的API响应，提取alarms字段
    if isinstance(data, dict) and 'alarms' in data:
        alarms = data['alarms']
        print(f"从API响应中提取到 {len(alarms)} 条告警")
    elif isinstance(data, list):
        alarms = data
        print(f"直接读取到 {len(alarms)} 条告警")
    else:
        print("数据格式不正确")
        return

    if not alarms:
        print("没有告警数据")
        return

    # 1) 打印字段全集
    print("\n" + "="*60)
    print("1. 字段结构分析")
    print("="*60)
    
    all_cols = set()
    for a in alarms:
        all_cols |= flatten_keys(a, depth=3)
    all_cols = sorted(all_cols)
    
    print(f"共发现字段(含嵌套路径) {len(all_cols)} 个：")
    for i, c in enumerate(all_cols, 1):
        print(f"  {i:2d}. {c}")

    # 2) 打印每个字段的若干样例值
    print("\n" + "="*60)
    print("2. 字段样例值分析（每个字段最多显示3个不同值）")
    print("="*60)
    
    samples = defaultdict(set)
    for a in alarms[:500]:  # 采样前500条
        flat = flatten(a, depth=3)
        for k, v in flat.items():
            if len(samples[k]) < 5:  # 每个字段最多收集5个不同值
                samples[k].add(str(v))

    for k in all_cols:
        sample_list = list(samples.get(k, []))[:3]
        print(f"{k}: {sample_list}")

    # 3) 分析同一站点下的字段差异
    print("\n" + "="*60)
    print("3. 同一站点下的字段差异分析")
    print("="*60)
    
    # 尝试不同的站点标识字段
    site_keys = [
        "ran_fm_alarm_site_name.value",
        "mename", 
        "alarmkey"
    ]
    
    for base_key in site_keys:
        if base_key not in all_cols:
            continue
            
        print(f"\n基于字段 '{base_key}' 分组分析:")
        
        buckets = defaultdict(list)
        for a in alarms:
            flat = flatten(a, depth=3)
            site_id = flat.get(base_key, "UNKNOWN")
            buckets[site_id].append(flat)

        # 找出有多条记录的站点
        multi_record_sites = [(site, rows) for site, rows in buckets.items() if len(rows) > 1]
        multi_record_sites.sort(key=lambda x: len(x[1]), reverse=True)
        
        print(f"  发现 {len(multi_record_sites)} 个站点有多条记录")
        
        # 分析前5个站点的差异
        for site, rows in multi_record_sites[:5]:
            print(f"\n  [{site}] 有 {len(rows)} 条记录:")
            
            # 找出值不全相同的字段
            diff_cols = []
            for col in all_cols:
                vals = set(str(r.get(col, "")) for r in rows)
                if len(vals) > 1:
                    diff_cols.append((col, list(vals)[:3]))
            
            if diff_cols:
                print(f"    存在 {len(diff_cols)} 个差异字段（显示前10个）：")
                for col, vals in diff_cols[:10]:
                    print(f"      {col}: {vals}")
            else:
                print("    所有字段值都相同（可能是重复数据）")
        
        break  # 只分析第一个可用的字段

    # 4) 时间字段分析
    print("\n" + "="*60)
    print("4. 时间字段分析（用于识别最新告警）")
    print("="*60)
    
    time_fields = [col for col in all_cols if 'time' in col.lower()]
    print(f"发现 {len(time_fields)} 个时间相关字段：")
    for tf in time_fields:
        sample_values = list(samples.get(tf, []))[:2]
        print(f"  {tf}: {sample_values}")

    # 5) 关键标识字段分析
    print("\n" + "="*60)
    print("5. 关键标识字段分析（用于生成唯一ID）")
    print("="*60)
    
    key_fields = [col for col in all_cols if any(keyword in col.lower() 
                  for keyword in ['key', 'id', 'code', 'name', 'position'])]
    print(f"发现 {len(key_fields)} 个标识相关字段：")
    for kf in key_fields:
        sample_values = list(samples.get(kf, []))[:2]
        print(f"  {kf}: {sample_values}")

    print(f"\n分析完成！数据文件: {json_file}")
    print(f"总告警数: {len(alarms)}")
    print(f"总字段数: {len(all_cols)}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
