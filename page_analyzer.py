#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页分析工具
用于分析告警页面的结构和数据
"""

import asyncio
from playwright.async_api import async_playwright
from config import ZTE_CONFIG
import json

class PageAnalyzer:
    def __init__(self):
        self.username = ZTE_CONFIG["username"]
        self.password = ZTE_CONFIG["password"]
        self.login_url = ZTE_CONFIG["login_url"]
        self.alarm_url = ZTE_CONFIG["alarm_url"]
        self.cookies = {}

    async def login_and_analyze(self):
        """登录并分析告警页面"""
        browser = None
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(
                    headless=False,  # 显示浏览器以便观察
                    args=[
                        '--ignore-certificate-errors',
                        '--ignore-ssl-errors',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )
                context = await browser.new_context(
                    ignore_https_errors=True,
                    viewport={'width': 1920, 'height': 1080}
                )
                page = await context.new_page()

                # 设置更长的超时时间
                page.set_default_timeout(60000)

                print(f"正在访问登录页面: {self.login_url}")
                await page.goto(self.login_url, wait_until='networkidle')

                print("页面加载完成，等待5秒...")
                await page.wait_for_timeout(5000)

                # 截图保存当前页面
                await page.screenshot(path='login_page.png')
                print("登录页面截图已保存为 login_page.png")

                # 分析登录页面
                await self._analyze_login_page(page)

                # 尝试登录
                login_success = await self._try_login(page)

                if login_success:
                    # 导航到告警页面
                    print(f"正在访问告警页面: {self.alarm_url}")
                    await page.goto(self.alarm_url, wait_until='networkidle')
                    await page.wait_for_timeout(5000)

                    # 截图保存告警页面
                    await page.screenshot(path='alarm_page.png')
                    print("告警页面截图已保存为 alarm_page.png")

                    # 分析页面结构
                    await self._analyze_page_structure(page)

                    # 查找网络请求
                    await self._monitor_network_requests(page)
                else:
                    print("登录失败，无法继续分析")

                print("\n分析完成！按任意键关闭浏览器...")
                input()

        except Exception as e:
            print(f"分析失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            if browser:
                try:
                    await browser.close()
                except:
                    pass

    async def _analyze_login_page(self, page):
        """分析登录页面结构"""
        print("\n=== 登录页面分析 ===")
        try:
            # 获取页面HTML
            content = await page.content()

            # 查找所有输入框
            inputs = await page.query_selector_all('input')
            print(f"找到 {len(inputs)} 个输入框:")

            for i, input_elem in enumerate(inputs):
                input_type = await input_elem.get_attribute('type') or 'text'
                input_name = await input_elem.get_attribute('name') or ''
                input_id = await input_elem.get_attribute('id') or ''
                input_class = await input_elem.get_attribute('class') or ''
                placeholder = await input_elem.get_attribute('placeholder') or ''

                print(f"  输入框{i+1}: type={input_type}, name={input_name}, id={input_id}, class={input_class}, placeholder={placeholder}")

            # 查找所有按钮
            buttons = await page.query_selector_all('button, input[type="submit"], input[type="button"]')
            print(f"\n找到 {len(buttons)} 个按钮:")

            for i, button in enumerate(buttons):
                button_text = await button.inner_text()
                button_type = await button.get_attribute('type') or ''
                button_id = await button.get_attribute('id') or ''
                button_class = await button.get_attribute('class') or ''

                print(f"  按钮{i+1}: text='{button_text}', type={button_type}, id={button_id}, class={button_class}")

        except Exception as e:
            print(f"登录页面分析失败: {e}")

    async def _try_login(self, page):
        """尝试登录"""
        try:
            print("\n=== 尝试登录 ===")

            # 等待页面完全加载
            await page.wait_for_load_state('networkidle')

            # 根据分析结果使用正确的选择器
            # 用户名输入框
            username_input = await page.query_selector('#inputUserName')
            if not username_input:
                username_input = await page.query_selector('input[name="username"]')

            if not username_input:
                print("未找到用户名输入框")
                return False
            else:
                print("找到用户名输入框: #inputUserName")

            # 密码输入框 - 使用正确的选择器
            password_input = await page.query_selector('#inputCiphercode')
            if not password_input:
                password_input = await page.query_selector('input[name="cipher_code"]')

            if not password_input:
                print("未找到密码输入框")
                return False
            else:
                print("找到密码输入框: #inputCiphercode")

            # 填写登录信息
            print("填写用户名...")
            await username_input.fill(self.username)

            print("填写密码...")
            await password_input.fill(self.password)

            # 查找登录按钮 - 使用正确的ID
            login_button = await page.query_selector('#loginBut')
            if not login_button:
                login_button = await page.query_selector('.loginButoon')

            if login_button:
                print("找到登录按钮: #loginBut")
                print("点击登录按钮...")
                await login_button.click()
            else:
                print("未找到登录按钮，尝试按回车键...")
                await password_input.press('Enter')

            # 等待登录完成
            print("等待登录完成...")
            await page.wait_for_timeout(5000)

            # 检查是否登录成功
            current_url = page.url
            print(f"登录后URL: {current_url}")

            if 'login' not in current_url.lower() and 'error' not in current_url.lower():
                print("登录可能成功")
                return True
            else:
                print("登录可能失败")
                return False

        except Exception as e:
            print(f"登录过程出错: {e}")
            return False

    async def _analyze_page_structure(self, page):
        """分析页面结构"""
        print("\n=== 页面结构分析 ===")
        
        try:
            # 获取页面标题
            title = await page.title()
            print(f"页面标题: {title}")
            
            # 查找表格
            tables = await page.query_selector_all('table')
            print(f"找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables):
                print(f"\n表格 {i+1}:")
                
                # 获取表头
                headers = await table.query_selector_all('th')
                if headers:
                    header_texts = []
                    for header in headers:
                        text = await header.inner_text()
                        header_texts.append(text.strip())
                    print(f"  表头: {header_texts}")
                
                # 获取前几行数据
                rows = await table.query_selector_all('tr')
                print(f"  总行数: {len(rows)}")
                
                for j, row in enumerate(rows[:3]):  # 只显示前3行
                    cells = await row.query_selector_all('td, th')
                    if cells:
                        cell_texts = []
                        for cell in cells:
                            text = await cell.inner_text()
                            cell_texts.append(text.strip()[:20])  # 限制长度
                        print(f"  第{j+1}行: {cell_texts}")
            
            # 查找其他可能的告警容器
            print(f"\n=== 查找告警相关元素 ===")
            
            alarm_selectors = [
                '.alarm', '[class*="alarm"]', '[id*="alarm"]',
                '.alert', '[class*="alert"]', '[id*="alert"]',
                '.fault', '[class*="fault"]', '[id*="fault"]',
                '.current', '[class*="current"]'
            ]
            
            for selector in alarm_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"找到 {len(elements)} 个 {selector} 元素")
                        
                        # 显示前几个元素的内容
                        for i, element in enumerate(elements[:2]):
                            text = await element.inner_text()
                            if text and len(text.strip()) > 5:
                                print(f"  元素{i+1}: {text.strip()[:100]}...")
                except:
                    continue
            
            # 查找iframe
            iframes = await page.query_selector_all('iframe')
            if iframes:
                print(f"\n找到 {len(iframes)} 个iframe")
                for i, iframe in enumerate(iframes):
                    src = await iframe.get_attribute('src')
                    print(f"  iframe{i+1}: {src}")

                    # 分析告警iframe
                    if 'active_alarm' in src or 'alarm' in src.lower():
                        print(f"\n=== 分析告警iframe ===")
                        await self._analyze_alarm_iframe(page, iframe)
                    
        except Exception as e:
            print(f"页面结构分析失败: {e}")

    async def _analyze_alarm_iframe(self, page, iframe):
        """分析告警iframe内容"""
        try:
            # 切换到iframe
            iframe_content = await iframe.content_frame()
            if not iframe_content:
                print("无法访问iframe内容")
                return

            # 等待iframe加载
            await iframe_content.wait_for_timeout(3000)

            # 截图保存iframe内容
            await iframe_content.screenshot(path='alarm_iframe.png')
            print("告警iframe截图已保存为 alarm_iframe.png")

            # 查找表格
            tables = await iframe_content.query_selector_all('table')
            print(f"iframe中找到 {len(tables)} 个表格")

            for i, table in enumerate(tables):
                print(f"\niframe表格 {i+1}:")

                # 获取表头
                headers = await table.query_selector_all('th')
                if headers:
                    header_texts = []
                    for header in headers:
                        text = await header.inner_text()
                        header_texts.append(text.strip())
                    print(f"  表头: {header_texts}")

                # 获取数据行
                rows = await table.query_selector_all('tr')
                print(f"  总行数: {len(rows)}")

                # 显示前几行数据
                for j, row in enumerate(rows[:5]):  # 显示前5行
                    cells = await row.query_selector_all('td, th')
                    if cells:
                        cell_texts = []
                        for cell in cells:
                            text = await cell.inner_text()
                            cell_texts.append(text.strip()[:30])  # 限制长度
                        print(f"  第{j+1}行: {cell_texts}")

            # 查找其他告警元素
            alarm_elements = await iframe_content.query_selector_all('[class*="alarm"], [class*="row"], [class*="data"]')
            if alarm_elements:
                print(f"\niframe中找到 {len(alarm_elements)} 个可能的告警元素")

                for i, element in enumerate(alarm_elements[:3]):
                    text = await element.inner_text()
                    if text and len(text.strip()) > 10:
                        print(f"  元素{i+1}: {text.strip()[:100]}...")

            # 监控iframe中的网络请求
            print(f"\n=== iframe网络请求监控 ===")

            def log_iframe_request(request):
                if any(keyword in request.url.lower() for keyword in ['alarm', 'data', 'query', 'list']):
                    print(f"iframe请求: {request.method} {request.url}")

            iframe_content.on('request', log_iframe_request)

            # 尝试刷新iframe数据
            try:
                refresh_buttons = await iframe_content.query_selector_all('button:has-text("刷新"), button:has-text("查询"), .refresh, [title*="刷新"]')
                if refresh_buttons:
                    print("点击iframe中的刷新按钮...")
                    await refresh_buttons[0].click()
                    await iframe_content.wait_for_timeout(3000)
            except Exception as e:
                print(f"iframe刷新失败: {e}")

        except Exception as e:
            print(f"分析告警iframe失败: {e}")

    async def _monitor_network_requests(self, page):
        """监控网络请求"""
        print(f"\n=== 网络请求监控 ===")
        
        requests_log = []
        
        def log_request(request):
            if any(keyword in request.url.lower() for keyword in ['alarm', 'alert', 'fault', 'current', 'api', 'data']):
                requests_log.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers)
                })
                print(f"捕获请求: {request.method} {request.url}")
        
        def log_response(response):
            if any(keyword in response.url.lower() for keyword in ['alarm', 'alert', 'fault', 'current', 'api', 'data']):
                print(f"响应: {response.status} {response.url}")
        
        page.on('request', log_request)
        page.on('response', log_response)
        
        # 刷新页面以捕获请求
        print("刷新页面以捕获网络请求...")
        await page.reload()
        await page.wait_for_timeout(5000)
        
        # 尝试触发一些操作
        try:
            # 点击刷新按钮
            refresh_selectors = [
                'button:has-text("刷新")', 'button:has-text("Refresh")',
                '.refresh', '[title*="刷新"]', '[onclick*="refresh"]'
            ]
            
            for selector in refresh_selectors:
                try:
                    await page.click(selector)
                    await page.wait_for_timeout(2000)
                    print(f"点击了刷新按钮: {selector}")
                    break
                except:
                    continue
                    
        except Exception as e:
            print(f"触发操作失败: {e}")
        
        # 保存请求日志
        if requests_log:
            with open('network_requests.json', 'w', encoding='utf-8') as f:
                json.dump(requests_log, f, indent=2, ensure_ascii=False)
            print(f"\n网络请求已保存到 network_requests.json")
            
            print(f"\n捕获到 {len(requests_log)} 个相关请求:")
            for req in requests_log:
                print(f"  {req['method']} {req['url']}")

async def main():
    analyzer = PageAnalyzer()
    await analyzer.login_and_analyze()

if __name__ == "__main__":
    asyncio.run(main())
