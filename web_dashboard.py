#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警监控系统 Web 可视化界面
提供实时监控状态、告警统计、组告警管理等功能
"""

import asyncio
import json
import os
import threading
import time
from datetime import datetime, timedelta
from flask import Flask, render_template, jsonify, request, redirect, url_for
from alarm_monitor import ZTEAlarmMonitor
from config import EMAIL_CONFIG, MONITOR_CONFIG

app = Flask(__name__)
app.secret_key = 'alarm_monitor_dashboard_2025'

# 全局变量
monitor_instance = None
monitor_thread = None
monitor_running = False
dashboard_data = {
    'status': '未启动',
    'last_check': None,
    'total_alarms': 0,
    'target_alarms': 0,
    'groups_count': 0,
    'notifications_sent': 0,
    'recent_notifications': [],
    'alarm_types': {},
    'important_groups': [],
    'system_logs': []
}

def update_dashboard_data(data):
    """更新仪表板数据"""
    global dashboard_data
    dashboard_data.update(data)

def get_log_tail(filename='alarm_monitor.log', lines=50):
    """获取日志文件的最后几行"""
    try:
        if not os.path.exists(filename):
            return []
        
        with open(filename, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            return [line.strip() for line in all_lines[-lines:]]
    except Exception as e:
        return [f"读取日志失败: {e}"]

def get_group_history():
    """获取组告警历史"""
    try:
        if os.path.exists('group_history.json'):
            with open('group_history.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except:
        return {}

@app.route('/')
def index():
    """主页 - 监控概览"""
    return render_template('dashboard.html', data=dashboard_data)

@app.route('/api/status')
def api_status():
    """API - 获取系统状态"""
    global monitor_running, dashboard_data, monitor_instance

    # 更新日志
    dashboard_data['system_logs'] = get_log_tail()

    # 更新组历史统计
    group_history = get_group_history()
    dashboard_data['groups_in_history'] = len(group_history)

    # 检查监控实例状态
    actual_status = '已停止'
    if monitor_running and monitor_instance:
        if hasattr(monitor_instance, 'page') and monitor_instance.page:
            actual_status = '运行中'
        else:
            actual_status = '登录中...'

    # 从日志中提取最新的统计信息
    logs = dashboard_data.get('system_logs', [])
    for log in reversed(logs[-10:]):  # 检查最近10条日志
        if '本轮检查完成' in log:
            try:
                # 解析日志中的统计信息
                if '总告警:' in log:
                    parts = log.split('总告警:')[1].split(',')
                    if len(parts) >= 3:
                        dashboard_data['total_alarms'] = int(parts[0].strip())
                        dashboard_data['target_alarms'] = int(parts[1].split('目标告警:')[1].strip())
                        dashboard_data['notifications_sent'] = int(parts[2].split('发送组通知:')[1].strip())
                break
            except:
                pass

    return jsonify({
        'status': actual_status,
        'monitor_running': monitor_running,
        **dashboard_data
    })

@app.route('/api/start', methods=['POST'])
def api_start():
    """API - 启动监控"""
    global monitor_instance, monitor_thread, monitor_running

    if monitor_running:
        return jsonify({'success': False, 'message': '监控已在运行中'})

    try:
        monitor_instance = ZTEAlarmMonitor()
        monitor_running = True

        def run_monitor():
            global monitor_running
            try:
                # 先尝试登录
                async def start_monitoring():
                    if await monitor_instance.login_with_playwright():
                        # 添加一个属性来存储当前告警
                        monitor_instance.current_alarms = []
                        await monitor_instance.monitor_alarms()
                    else:
                        global monitor_running
                        monitor_running = False
                        update_dashboard_data({
                            'status': '登录失败',
                        })

                asyncio.run(start_monitoring())
            except Exception as e:
                print(f"监控线程异常: {e}")
                monitor_running = False
                update_dashboard_data({
                    'status': f'运行异常: {e}',
                })

        monitor_thread = threading.Thread(target=run_monitor, daemon=True)
        monitor_thread.start()

        update_dashboard_data({
            'status': '启动中...',
            'last_check': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

        return jsonify({'success': True, 'message': '监控启动中，正在登录网管系统...'})
    except Exception as e:
        monitor_running = False
        return jsonify({'success': False, 'message': f'启动失败: {e}'})

@app.route('/api/stop', methods=['POST'])
def api_stop():
    """API - 停止监控"""
    global monitor_instance, monitor_running
    
    if not monitor_running:
        return jsonify({'success': False, 'message': '监控未在运行'})
    
    try:
        monitor_running = False
        if monitor_instance:
            # 这里可以添加优雅停止的逻辑
            pass
        
        update_dashboard_data({
            'status': '已停止',
            'notifications_sent': 0
        })
        
        return jsonify({'success': True, 'message': '监控已停止'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'停止失败: {e}'})

@app.route('/groups')
def groups():
    """组告警管理页面"""
    group_history = get_group_history()
    return render_template('groups.html', groups=group_history)

@app.route('/api/alarms')
def api_alarms():
    """API - 获取目标告警数据"""
    global monitor_instance

    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    search = request.args.get('search', '', type=str)
    role_filter = request.args.get('role', '', type=str)
    sort_by = request.args.get('sort', 'time', type=str)
    sort_order = request.args.get('order', 'desc', type=str)

    try:
        if monitor_instance and hasattr(monitor_instance, 'current_alarms'):
            all_alarms = monitor_instance.current_alarms or []
        else:
            all_alarms = []

        # 只获取目标告警（经过筛选的告警）
        target_alarms = []
        for alarm in all_alarms:
            if monitor_instance and monitor_instance.is_target_alarm(alarm):
                target_alarms.append(alarm)

        # 转换为表格数据格式，直接使用原始数据
        table_data = []
        for alarm in target_alarms:
            raw = alarm.get('原始数据', {})

            # 直接从原始数据获取字段值
            def get_raw_value(field_data, default=''):
                if isinstance(field_data, dict):
                    return field_data.get('value', field_data.get('displayname', default))
                return str(field_data) if field_data is not None else default

            # 获取基站名称
            station_name = get_raw_value(raw.get('ran_fm_alarm_site_name', ''), '未知站点')
            if not station_name or station_name == '未知站点':
                station_name = raw.get('mename', '未知站点')

            # 获取告警内容
            alarm_content = get_raw_value(raw.get('alarmtitle', ''), '')
            if not alarm_content:
                alarm_content = raw.get('codename', '未知告警')

            # 获取告警对象
            alarm_object = get_raw_value(raw.get('ran_fm_alarm_object_name', ''), '')
            if not alarm_object:
                alarm_object = get_raw_value(raw.get('ran_fm_alarm_object_id', ''), '未知对象')

            # 获取状态信息
            clear_status = raw.get('clearstatename', '未知')
            ack_status = raw.get('ackstatename', '未知')

            # 格式化时间
            alarm_time = ''
            if 'alarmraisedtime' in raw:
                try:
                    timestamp = raw['alarmraisedtime']
                    if isinstance(timestamp, (int, float)):
                        from datetime import datetime
                        dt = datetime.fromtimestamp(int(timestamp) / 1000)
                        alarm_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    alarm_time = str(raw.get('alarmraisedtime', ''))

            # 计算持续时间
            duration = '未知'
            try:
                if monitor_instance and alarm_time:
                    raised_dt = monitor_instance.get_alarm_raised_dt(raw)
                    if raised_dt:
                        duration_hours = monitor_instance.calculate_duration_since(raised_dt)
                        if duration_hours < 1:
                            duration = f"{int(duration_hours * 60)}分钟"
                        else:
                            duration = f"{duration_hours:.1f}小时"
            except:
                pass

            # 获取告警角色
            role = '未知'
            if monitor_instance:
                try:
                    role = monitor_instance.classify_role(raw)
                except:
                    role = '独立'

            table_data.append({
                'time': alarm_time,
                'station': station_name,
                'content': alarm_content,
                'object': alarm_object,
                'role': role,
                'clear_status': clear_status,
                'ack_status': ack_status,
                'duration': duration,
                'alarm_id': raw.get('alarmkey', raw.get('id', '')),
                'severity': raw.get('perceivedseverityname', ''),
                'alarm_type': raw.get('alarmtypename', ''),
                'additional_text': raw.get('additionaltext', ''),
                'reason': raw.get('reasonname', '')
            })

        # 搜索过滤
        if search:
            table_data = [item for item in table_data if
                         search.lower() in item['station'].lower() or
                         search.lower() in item['content'].lower()]

        # 角色过滤
        if role_filter:
            table_data = [item for item in table_data if item['role'] == role_filter]

        # 排序
        reverse = sort_order == 'desc'
        if sort_by == 'time':
            table_data.sort(key=lambda x: x['time'], reverse=reverse)
        elif sort_by == 'station':
            table_data.sort(key=lambda x: x['station'], reverse=reverse)
        elif sort_by == 'role':
            table_data.sort(key=lambda x: x['role'], reverse=reverse)

        # 分页
        total = len(table_data)
        start = (page - 1) * per_page
        end = start + per_page
        items = table_data[start:end]

        return jsonify({
            'items': items,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        return jsonify({
            'items': [],
            'total': 0,
            'page': 1,
            'per_page': per_page,
            'pages': 0,
            'error': str(e)
        })

@app.route('/api/raw_data_sample')
def api_raw_data_sample():
    """API - 获取原始数据样例"""
    global monitor_instance

    try:
        if monitor_instance and hasattr(monitor_instance, 'current_alarms'):
            alarms = monitor_instance.current_alarms or []
            if alarms:
                # 取前3条告警的原始数据作为样例
                samples = []
                for i, alarm in enumerate(alarms[:3]):
                    sample_raw = alarm.get('原始数据', {})
                    samples.append({
                        'index': i + 1,
                        'fields': list(sample_raw.keys()),
                        'data': sample_raw
                    })

                # 获取所有字段的并集
                all_fields = set()
                for sample in samples:
                    all_fields.update(sample['fields'])

                # 重点关注的字段
                key_fields = [
                    'alarmkey', 'alarmraisedtime', 'alarmtitle', 'codename',
                    'mename', 'ran_fm_alarm_site_name', 'ran_fm_alarm_object_name',
                    'ran_fm_alarm_object_id', 'clearstatename', 'ackstatename',
                    'perceivedseverityname', 'alarmtypename', 'additionaltext',
                    'reasonname', 'componentdn', 'alarmcategory'
                ]

                # 提取关键字段的样例数据
                key_data = {}
                if samples:
                    first_sample = samples[0]['data']
                    for field in key_fields:
                        if field in first_sample:
                            value = first_sample[field]
                            if isinstance(value, dict):
                                key_data[field] = {
                                    'type': 'dict',
                                    'keys': list(value.keys()),
                                    'value': value.get('value', ''),
                                    'displayname': value.get('displayname', ''),
                                    'full': value
                                }
                            else:
                                key_data[field] = {
                                    'type': type(value).__name__,
                                    'value': str(value)[:200] + ('...' if len(str(value)) > 200 else '')
                                }

                return jsonify({
                    'success': True,
                    'total_samples': len(samples),
                    'total_unique_fields': len(all_fields),
                    'all_fields': sorted(list(all_fields)),
                    'key_fields_data': key_data,
                    'samples': [
                        {
                            'index': sample['index'],
                            'field_count': len(sample['fields']),
                            'sample_fields': sample['fields'][:10]  # 只显示前10个字段
                        } for sample in samples
                    ]
                })

        return jsonify({
            'success': False,
            'message': '暂无告警数据，请先启动监控系统',
            'total_samples': 0,
            'total_unique_fields': 0,
            'all_fields': [],
            'key_fields_data': {},
            'samples': []
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取失败: {e}',
            'total_samples': 0,
            'total_unique_fields': 0,
            'all_fields': [],
            'key_fields_data': {},
            'samples': [],
            'error_details': str(e)
        })

@app.route('/api/current_alarm_types')
def api_current_alarm_types():
    """API - 获取当前的ALARM_TYPES配置"""
    try:
        from config import ALARM_TYPES
        return jsonify({
            'success': True,
            'alarm_types': ALARM_TYPES,
            'total_types': len(ALARM_TYPES),
            'total_keywords': sum(len(config.get('keywords', [])) for config in ALARM_TYPES.values())
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'alarm_types': {},
            'total_types': 0,
            'total_keywords': 0
        })

@app.route('/api/groups')
def api_groups():
    """API - 获取组告警数据"""
    return jsonify(get_group_history())

@app.route('/api/clear_history', methods=['POST'])
def api_clear_history():
    """API - 清除历史记录"""
    try:
        if os.path.exists('group_history.json'):
            os.remove('group_history.json')
        return jsonify({'success': True, 'message': '历史记录已清除'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'清除失败: {e}'})

@app.route('/config')
def config():
    """配置管理页面"""
    try:
        from config import ALARM_TYPES
        target_alarm_types = ALARM_TYPES
        exclude_keywords = []
    except ImportError:
        target_alarm_types = {}
        exclude_keywords = []

    return render_template('config.html',
                         email_config=EMAIL_CONFIG,
                         monitor_config=MONITOR_CONFIG,
                         target_alarm_types=target_alarm_types,
                         exclude_keywords=exclude_keywords)

@app.route('/logs')
def logs():
    """日志查看页面"""
    logs = get_log_tail(lines=200)
    return render_template('logs.html', logs=logs)

@app.route('/api/logs')
def api_logs():
    """API - 获取最新日志"""
    return jsonify({'logs': get_log_tail()})

if __name__ == '__main__':
    print("启动告警监控系统 Web 界面...")
    print("访问地址: http://localhost:8080")
    print("按 Ctrl+C 停止服务")

    app.run(host='0.0.0.0', port=8080, debug=False)
