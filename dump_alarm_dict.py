#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警字典统计工具
用于分析和统计网管系统中的所有告警类型、代码、名称等信息
"""

import asyncio
import json
from collections import Counter, defaultdict
from datetime import datetime
import pandas as pd
from alarm_monitor import ZTEAlarmMonitor

class AlarmDictAnalyzer:
    def __init__(self):
        self.monitor = ZTEAlarmMonitor()
        self.all_alarms = []
        self.alarm_stats = {
            'by_code': Counter(),
            'by_title': Counter(),
            'by_type': Counter(),
            'by_severity': Counter(),
            'by_clear_status': Counter(),
            'by_ack_status': Counter(),
            'code_to_title': {},
            'title_to_details': defaultdict(set)
        }
    
    async def collect_alarms(self, max_alarms=10000):
        """收集告警数据"""
        print("正在登录网管系统...")
        if not await self.monitor.login_with_playwright():
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功，开始收集告警数据...")
        
        # 获取告警数据
        raw_alarms = await self.monitor.fetch_all_current_alarms(
            pagesize=500, 
            max_alarms=max_alarms
        )
        
        if not raw_alarms:
            print("❌ 未获取到告警数据")
            return False
        
        print(f"✅ 成功获取 {len(raw_alarms)} 条原始告警数据")
        
        # 解析告警数据
        for raw_alarm in raw_alarms:
            alarm = self.monitor._parse_alarm_data(raw_alarm)
            if alarm:
                self.all_alarms.append({
                    'parsed': alarm,
                    'raw': raw_alarm
                })
        
        print(f"✅ 成功解析 {len(self.all_alarms)} 条告警数据")
        return True
    
    def analyze_alarms(self):
        """分析告警数据"""
        print("正在分析告警数据...")
        
        for alarm_data in self.all_alarms:
            raw = alarm_data['raw']
            parsed = alarm_data['parsed']
            
            # 提取关键字段
            def get_field_value(field_data, default='未知'):
                if isinstance(field_data, dict):
                    return field_data.get('value', field_data.get('displayname', default))
                return str(field_data) if field_data is not None else default
            
            # 告警代码
            alarm_code = raw.get('alarmcode', '未知代码')
            
            # 告警标题/名称
            alarm_title = get_field_value(raw.get('alarmtitle', ''), '未知标题')
            if not alarm_title or alarm_title == '未知标题':
                alarm_title = raw.get('codename', '未知标题')
            
            # 告警类型
            alarm_type = get_field_value(raw.get('alarmtypename', ''), '未知类型')
            
            # 告警级别
            severity = raw.get('perceivedseverityname', '未知级别')
            
            # 清除状态
            clear_status = raw.get('clearstatename', '未知')
            
            # 确认状态
            ack_status = raw.get('ackstatename', '未知')
            
            # 附加信息
            additional_text = raw.get('additionaltext', '')
            reason = raw.get('reasonname', '')
            
            # 统计
            self.alarm_stats['by_code'][alarm_code] += 1
            self.alarm_stats['by_title'][alarm_title] += 1
            self.alarm_stats['by_type'][alarm_type] += 1
            self.alarm_stats['by_severity'][severity] += 1
            self.alarm_stats['by_clear_status'][clear_status] += 1
            self.alarm_stats['by_ack_status'][ack_status] += 1
            
            # 建立映射关系
            self.alarm_stats['code_to_title'][alarm_code] = alarm_title
            self.alarm_stats['title_to_details'][alarm_title].add(
                f"类型:{alarm_type}|级别:{severity}|附加:{additional_text[:50]}"
            )
        
        print("✅ 告警数据分析完成")
    
    def print_statistics(self, top_n=50):
        """打印统计结果"""
        print("\n" + "="*80)
        print("告警字典统计报告")
        print("="*80)
        print(f"统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总告警数: {len(self.all_alarms)}")
        print(f"唯一告警代码数: {len(self.alarm_stats['by_code'])}")
        print(f"唯一告警标题数: {len(self.alarm_stats['by_title'])}")
        print(f"唯一告警类型数: {len(self.alarm_stats['by_type'])}")
        
        # 1. 按告警代码统计 (TOP N)
        print(f"\n📊 告警代码统计 (TOP {top_n})")
        print("-" * 80)
        print(f"{'告警代码':<15} {'出现次数':<10} {'告警标题':<50}")
        print("-" * 80)
        for code, count in self.alarm_stats['by_code'].most_common(top_n):
            title = self.alarm_stats['code_to_title'].get(code, '未知')
            print(f"{str(code):<15} {count:<10} {title:<50}")
        
        # 2. 按告警标题统计 (TOP N)
        print(f"\n📋 告警标题统计 (TOP {top_n})")
        print("-" * 80)
        print(f"{'告警标题':<50} {'出现次数':<10}")
        print("-" * 80)
        for title, count in self.alarm_stats['by_title'].most_common(top_n):
            print(f"{title:<50} {count:<10}")
        
        # 3. 按告警类型统计
        print(f"\n📂 告警类型统计")
        print("-" * 50)
        for alarm_type, count in self.alarm_stats['by_type'].most_common():
            print(f"{alarm_type:<30} {count:<10}")
        
        # 4. 按告警级别统计
        print(f"\n⚠️  告警级别统计")
        print("-" * 30)
        for severity, count in self.alarm_stats['by_severity'].most_common():
            print(f"{severity:<15} {count:<10}")
        
        # 5. 按状态统计
        print(f"\n🔄 清除状态统计")
        print("-" * 30)
        for status, count in self.alarm_stats['by_clear_status'].most_common():
            print(f"{status:<15} {count:<10}")
        
        print(f"\n✅ 确认状态统计")
        print("-" * 30)
        for status, count in self.alarm_stats['by_ack_status'].most_common():
            print(f"{status:<15} {count:<10}")
    
    def export_to_files(self):
        """导出到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 1. 导出完整的告警字典 (JSON)
        alarm_dict = {
            'export_time': datetime.now().isoformat(),
            'total_alarms': len(self.all_alarms),
            'statistics': {
                'by_code': dict(self.alarm_stats['by_code'].most_common()),
                'by_title': dict(self.alarm_stats['by_title'].most_common()),
                'by_type': dict(self.alarm_stats['by_type'].most_common()),
                'by_severity': dict(self.alarm_stats['by_severity'].most_common()),
                'code_to_title': self.alarm_stats['code_to_title']
            }
        }
        
        json_file = f"alarm_dict_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(alarm_dict, f, ensure_ascii=False, indent=2)
        print(f"✅ 告警字典已导出到: {json_file}")
        
        # 2. 导出Excel格式
        try:
            # 告警代码统计表
            code_data = []
            for code, count in self.alarm_stats['by_code'].most_common():
                title = self.alarm_stats['code_to_title'].get(code, '未知')
                code_data.append({
                    '告警代码': code,
                    '告警标题': title,
                    '出现次数': count
                })
            
            # 告警标题统计表
            title_data = []
            for title, count in self.alarm_stats['by_title'].most_common():
                title_data.append({
                    '告警标题': title,
                    '出现次数': count
                })
            
            excel_file = f"alarm_dict_{timestamp}.xlsx"
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                pd.DataFrame(code_data).to_excel(writer, sheet_name='按代码统计', index=False)
                pd.DataFrame(title_data).to_excel(writer, sheet_name='按标题统计', index=False)
                pd.DataFrame(list(self.alarm_stats['by_type'].items()), 
                           columns=['告警类型', '出现次数']).to_excel(writer, sheet_name='按类型统计', index=False)
                pd.DataFrame(list(self.alarm_stats['by_severity'].items()), 
                           columns=['告警级别', '出现次数']).to_excel(writer, sheet_name='按级别统计', index=False)
            
            print(f"✅ Excel报告已导出到: {excel_file}")
        except ImportError:
            print("⚠️  pandas或openpyxl未安装，跳过Excel导出")
        
        # 3. 导出建议的ALARM_TYPES配置
        self.generate_alarm_types_suggestion(timestamp)
    
    def generate_alarm_types_suggestion(self, timestamp):
        """生成ALARM_TYPES配置建议"""
        print("\n🔧 生成ALARM_TYPES配置建议...")
        
        # 基于高频告警标题生成建议配置
        suggestions = {
            "电源故障": {
                "keywords": [],
                "priority": "high",
                "description": "电源相关故障"
            },
            "传输故障": {
                "keywords": [],
                "priority": "high", 
                "description": "传输链路故障"
            },
            "设备故障": {
                "keywords": [],
                "priority": "critical",
                "description": "设备硬件故障"
            },
            "小区故障": {
                "keywords": [],
                "priority": "high",
                "description": "小区服务故障"
            }
        }
        
        # 分析高频告警标题，提取关键词
        for title, count in self.alarm_stats['by_title'].most_common(100):
            title_lower = title.lower()
            
            if any(kw in title_lower for kw in ['电源', '掉电', '停电', '市电', '电池']):
                suggestions["电源故障"]["keywords"].append(title)
            elif any(kw in title_lower for kw in ['传输', '光纤', '光口', '链路', 'e1', 'eth']):
                suggestions["传输故障"]["keywords"].append(title)
            elif any(kw in title_lower for kw in ['rru', 'bbu', 'aau', 'du', 'cu', '设备']):
                suggestions["设备故障"]["keywords"].append(title)
            elif any(kw in title_lower for kw in ['小区', 'cell', 'lte', 'nr', '退服']):
                suggestions["小区故障"]["keywords"].append(title)
        
        # 保存建议配置
        suggestion_file = f"alarm_types_suggestion_{timestamp}.py"
        with open(suggestion_file, 'w', encoding='utf-8') as f:
            f.write("# 基于告警统计生成的ALARM_TYPES配置建议\n")
            f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 基于 {len(self.all_alarms)} 条告警数据分析\n\n")
            f.write("ALARM_TYPES_SUGGESTION = {\n")
            
            for category, config in suggestions.items():
                if config["keywords"]:  # 只输出有关键词的类别
                    f.write(f'    "{category}": {{\n')
                    f.write(f'        "keywords": {config["keywords"][:10]},  # 取前10个高频关键词\n')
                    f.write(f'        "priority": "{config["priority"]}",\n')
                    f.write(f'        "description": "{config["description"]}"\n')
                    f.write('    },\n')
            
            f.write("}\n")
        
        print(f"✅ ALARM_TYPES配置建议已保存到: {suggestion_file}")

async def main():
    """主函数"""
    print("告警字典统计工具")
    print("=" * 50)
    
    analyzer = AlarmDictAnalyzer()
    
    try:
        # 收集告警数据
        if not await analyzer.collect_alarms(max_alarms=20000):
            return
        
        # 分析数据
        analyzer.analyze_alarms()
        
        # 打印统计结果
        analyzer.print_statistics(top_n=50)
        
        # 导出文件
        analyzer.export_to_files()
        
        print("\n✅ 告警字典统计完成！")
        print("📁 生成的文件:")
        print("   - alarm_dict_YYYYMMDD_HHMMSS.json (完整统计数据)")
        print("   - alarm_dict_YYYYMMDD_HHMMSS.xlsx (Excel报告)")
        print("   - alarm_types_suggestion_YYYYMMDD_HHMMSS.py (配置建议)")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if hasattr(analyzer.monitor, 'page') and analyzer.monitor.page:
            await analyzer.monitor.page.close()
        if hasattr(analyzer.monitor, 'browser') and analyzer.monitor.browser:
            await analyzer.monitor.browser.close()

if __name__ == "__main__":
    asyncio.run(main())
