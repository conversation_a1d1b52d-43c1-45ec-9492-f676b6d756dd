#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单文件：抓取中兴网管“当前告警”原始数据（不作任何处理）
依赖：playwright (pip install playwright && playwright install)

功能步骤：
1) 启动 Chromium，忽略自签名证书。
2) 打开登录页，按选择器输入用户名/密码并点击登录。
3) 进入“当前告警”路由页（或兜底点击菜单）。
4) 在页内点击“刷新/查询”触发数据请求，并嗅探真实接口与请求体。
5) 使用嗅探到的接口与请求体进行分页抓取，合并所有 alarms。
6) 将“所有告警原始对象”保存为一个 JSON 数组文件。

仅导出原始 alarms 列表（不包裹额外元数据）。
"""

import os
import re
import json
import time
import argparse
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Tuple, Optional, List

from playwright.async_api import async_playwright

# ----------------------------
# 默认配置（可用命令行覆盖或用环境变量传）
# ----------------------------
DEFAULT_BASE_URL = "https://*************:28001"
DEFAULT_LOGIN_URL = "https://*************:28001/uportal/framework/default.html"
DEFAULT_ALARM_URL = "https://*************:28001/uportal/framework/default.html#/_ngict-fm-currentAlarm"

# 登录选择器（来自你提供的页面）
LOGIN_SELECTORS = {
    "username_input": "#inputUserName",
    "password_input": "#inputCiphercode",
    "login_button":  "#loginBut",
}

# 备用菜单选择器（若直达 hash 路由不起效）
MENU_SELECTORS = {
    "alarm_menu": 'text="告警管理"',
    "current_alarm": 'text="当前告警"',
}

# 能触发表格接口请求的按钮/元素（逐个尝试，命中一个即可）
REFRESH_TRIGGERS = [
    'button:has-text("刷新")',
    'button:has-text("查询")',
    '[title*="刷新"]',
    '#refreshBtn',
    '#searchBtn',
    '.icon-refresh',
]

# 判定是“当前告警表格接口”的 URL 片段（放宽匹配以适配版本差异）
API_URL_SUBSTRINGS = [
    "/api/fm-active/v1/activealarms/table",
    "/activealarms/table",
    "/activealarms/list",
    "/activealarms",     # 最宽
]


def log_info(msg: str) -> None:
    print(f"[INFO] {msg}", flush=True)


def log_warn(msg: str) -> None:
    print(f"[WARN] {msg}", flush=True)


def log_err(msg: str) -> None:
    print(f"[ERROR] {msg}", flush=True)


async def login(page, login_url: str, username: str, password: str, timeout_ms: int = 30000) -> None:
    log_info(f"打开登录页: {login_url}")
    await page.goto(login_url, timeout=timeout_ms)
    # 某些站点首次加载较慢，等到网络空闲再小等
    await page.wait_for_load_state("networkidle")
    await page.wait_for_timeout(1500)

    # 输入凭据并登录
    await page.wait_for_selector(LOGIN_SELECTORS["username_input"], timeout=timeout_ms)
    await page.fill(LOGIN_SELECTORS["username_input"], username)
    await page.fill(LOGIN_SELECTORS["password_input"], password)
    await page.click(LOGIN_SELECTORS["login_button"])
    # 登录后给一点时间
    await page.wait_for_timeout(3500)

    log_info(f"登录后 URL: {page.url}")


async def ensure_on_alarm_page(page, alarm_url: str, timeout_ms: int = 40000) -> None:
    """
    尝试直接跳转 hash 路由；若不生效，兜底点击菜单“告警管理”->“当前告警”。
    """
    log_info(f"进入当前告警页: {alarm_url}")
    await page.goto(alarm_url, timeout=timeout_ms)
    await page.wait_for_load_state("networkidle")
    await page.wait_for_timeout(2000)

    # 如果路由框架不执行，菜单兜底
    try:
        # 尝试能否看到表格或明显的页面元素；若看不到，点菜单
        # 这里不强依赖表格选择器，直接尝试菜单兜底，不影响能加载的情形
        await page.click(MENU_SELECTORS["alarm_menu"], timeout=2000)
        await page.wait_for_timeout(1000)
        await page.click(MENU_SELECTORS["current_alarm"], timeout=2000)
        await page.wait_for_timeout(2500)
        log_info("已通过菜单兜底进入‘当前告警’页面。")
    except Exception:
        # 菜单不存在或已在正确页面，忽略
        pass


def _looks_like_alarm_api(url: str) -> bool:
    u = url.lower()
    return any(s in u for s in API_URL_SUBSTRINGS)


async def sniff_request_body_once(page, timeout_ms: int = 60000) -> Tuple[Optional[str], Optional[Dict[str, Any]], Optional[Dict[str, str]]]:
    """
    在页面中等待一次“当前告警表格”的 POST 请求，返回 (api_url, body_dict, headers_dict)。
    通过点击若干“刷新/查询”按钮来主动触发。
    """
    log_info("开始嗅探表格请求体 ...")

    # 先等页面空闲
    await page.wait_for_load_state("networkidle")
    await page.wait_for_timeout(1500)

    async def _try_triggers():
        # 逐个尝试触发器，命中一个即可
        for sel in REFRESH_TRIGGERS:
            try:
                el = await page.query_selector(sel)
                if el:
                    log_info(f"尝试点击触发器: {sel}")
                    await el.click()
                    await page.wait_for_timeout(1200)
            except Exception:
                continue

    # 先点一轮触发器
    await _try_triggers()

    # 等待一次满足条件的 request 事件
    def _pred(req) -> bool:
        try:
            return req.method == "POST" and _looks_like_alarm_api(req.url)
        except Exception:
            return False

    try:
        req = await page.wait_for_event("request", predicate=_pred, timeout=timeout_ms)
    except Exception:
        # 若首次等待不到，再点一轮重试
        await _try_triggers()
        req = await page.wait_for_event("request", predicate=_pred, timeout=timeout_ms)

    api_url = req.url
    headers = dict(req.headers)
    body_text = req.post_data

    if not body_text:
        log_warn("已捕获到请求，但 post_data 为空。")
        return api_url, None, headers

    try:
        body = json.loads(body_text)
    except Exception:
        log_warn("捕获到的请求体不是有效 JSON，原样透传。")
        body = {"__raw_text__": body_text}

    log_info(f"已捕获真实接口: {api_url}")
    return api_url, body, headers


async def post_with_fallback(context, page, url: str, body: Dict[str, Any], sniffed_headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """
    先尝试 context.request.post（更快），失败或结构异常则回退到 page.evaluate(fetch)。
    """
    hdrs = {
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json",
    }
    # 合并页面嗅探到的请求头（去掉会引起问题的键）
    if sniffed_headers:
        for k, v in sniffed_headers.items():
            lk = k.lower()
            if lk not in ("host", "content-length", "connection"):
                hdrs[k] = v

    try:
        resp = await context.request.post(url, data=json.dumps(body), headers=hdrs)
        txt = await resp.text()
        try:
            data = json.loads(txt)
        except Exception:
            data = {"__raw_text__": txt}
        # 简单校验
        if isinstance(data, dict) and ("alarms" in data or "rows" in data):
            return data
        # 某些实现直接返回数组
        if isinstance(data, list):
            return {"alarms": data}
        log_warn(f"context.request.post 返回结构异常，回退到页面内 fetch。keys={list(data) if isinstance(data, dict) else type(data)}")
    except Exception as e:
        log_warn(f"context.request.post 异常：{e}，回退到页面内 fetch。")

    # 回退：在页面环境中执行 fetch
    js = """
    async (u, b, h) => {
      const resp = await fetch(u, {method: 'POST', headers: h, body: JSON.stringify(b)});
      const txt = await resp.text();
      try { return JSON.parse(txt); } catch(e) { return {"__raw_text__": txt}; }
    }
    """
    data = await page.evaluate(js, url, body, hdrs)
    if isinstance(data, list):
        data = {"alarms": data}
    return data if isinstance(data, dict) else {"__raw_text__": str(data)}


async def fetch_all_alarms(context, page, api_url: str, base_body: Dict[str, Any], sniffed_headers: Optional[Dict[str, str]],
                           pagesize: int, max_alarms: int) -> List[Dict[str, Any]]:
    """
    依据真实请求体分页抓取，合并所有 alarms。
    - 不改动任何字段名；
    - 仅设置 pagesize/page/timestamp/queryid 等分页所需键。
    """
    import copy

    all_alarms: List[Dict[str, Any]] = []
    body = copy.deepcopy(base_body) if isinstance(base_body, dict) else {}

    # 适配常见分页键
    body["pagesize"] = pagesize
    body["page"] = 1
    # 让后端重新生成 queryid（如存在）
    body.pop("queryid", None)

    queryid = None
    page_no = 1

    while len(all_alarms) < max_alarms:
        body["page"] = page_no
        # 常见实现需要一个 ISO UTC 时间戳
        body["timestamp"] = datetime.now(timezone.utc).isoformat(timespec="milliseconds").replace("+00:00", "Z")
        if queryid:
            body["queryid"] = queryid

        data = await post_with_fallback(context, page, api_url, body, sniffed_headers)

        # 尽可能兼容不同字段
        if isinstance(data, dict):
            rows = data.get("alarms") or data.get("rows") or []
            if not isinstance(rows, list):
                log_warn(f"第{page_no}页返回的 alarms/rows 不是列表，终止。")
                break
            log_info(f"第{page_no}页获取 {len(rows)} 条")
            all_alarms.extend(rows)
            if not queryid:
                queryid = data.get("queryid")
        else:
            log_warn(f"第{page_no}页返回非字典结构，终止。type={type(data)}")
            break

        if len(rows) < pagesize:
            break

        page_no += 1
        # 避免过快
        await page.wait_for_timeout(150)

        if len(all_alarms) >= max_alarms:
            break

    return all_alarms


def build_arg_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="抓取中兴网管‘当前告警’原始数据（不做处理）")
    p.add_argument("--base-url", default=DEFAULT_BASE_URL)
    p.add_argument("--login-url", default=DEFAULT_LOGIN_URL)
    p.add_argument("--alarm-url", default=DEFAULT_ALARM_URL)
    p.add_argument("--username", default=os.getenv("ZTE_USER", ""))
    p.add_argument("--password", default=os.getenv("ZTE_PASS", ""))
    p.add_argument("--pagesize", type=int, default=500)
    p.add_argument("--max-alarms", type=int, default=50000)
    p.add_argument("--sniff-timeout", type=int, default=60000, help="嗅探单次超时(ms)")
    p.add_argument("--headless", action="store_true", help="无头模式（默认开启）", default=True)
    p.add_argument("--show-browser", action="store_true", help="显示浏览器窗口（调试用）")
    p.add_argument("--outfile", default="", help="输出文件路径（.json）。默认自动命名。")
    return p


def default_outfile() -> str:
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"zte_active_alarms_raw_{ts}.json"


async def main_async(args) -> int:
    if not args.username or not args.password:
        log_err("缺少账号或口令。建议通过环境变量 ZTE_USER / ZTE_PASS 或命令行参数 --username/--password 传入。")
        return 2

    outpath = args.outfile or default_outfile()

    launch_headless = not args.show_browser  # show-browser 优先
    all_alarms: List[Dict[str, Any]] = []

    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=launch_headless,
            args=["--ignore-certificate-errors", "--ignore-ssl-errors"]
        )
        context = await browser.new_context(ignore_https_errors=True)
        page = await context.new_page()

        try:
            # 登录
            await login(page, args.login_url, args.username, args.password)

            # 进入当前告警页
            await ensure_on_alarm_page(page, args.alarm_url)

            # 嗅探一次真实接口与请求体
            api_url, base_body, sniffed_headers = await sniff_request_body_once(
                page, timeout_ms=args.sniff_timeout
            )
            if not api_url:
                log_err("未能嗅探到任何‘当前告警’接口请求，无法继续。请确认刷新/查询按钮选择器是否可用。")
                return 3
            if not isinstance(base_body, dict):
                log_warn("嗅探到的请求体不是 JSON 对象，将尝试使用空对象作为基础请求体。")
                base_body = {}

            # 分页抓取
            t0 = time.time()
            all_alarms = await fetch_all_alarms(
                context=context,
                page=page,
                api_url=api_url,
                base_body=base_body,
                sniffed_headers=sniffed_headers,
                pagesize=args.pagesize,
                max_alarms=args.max_alarms,
            )
            dt = time.time() - t0
            log_info(f"抓取完成，共 {len(all_alarms)} 条，耗时 {dt:.2f} 秒")

        finally:
            await context.close()
            await browser.close()

    # 仅输出原始 alarms 列表（不包裹其他元数据）
    with open(outpath, "w", encoding="utf-8") as f:
        json.dump(all_alarms, f, ensure_ascii=False, indent=2)
    log_info(f"已写入：{outpath}")
    return 0


def main():
    parser = build_arg_parser()
    args = parser.parse_args()
    try:
        asyncio.run(main_async(args))
    except KeyboardInterrupt:
        log_warn("用户中断。")
    except Exception as e:
        log_err(f"运行异常：{e}")
        raise


if __name__ == "__main__":
    main()
