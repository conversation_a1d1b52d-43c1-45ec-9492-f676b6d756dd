#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取“当前活动告警”的原始数据（不做任何处理）
- 登录中兴网管
- 嗅探前端调用的 /api/fm-active/v1/activealarms/table 请求体
- 按页拉取全部告警（保持原始字段与结构）
- 保存为 JSON 文件：包含每页完整响应 + 汇总后的所有 alarms

使用:
  python fetch_zte_active_alarms_raw.py --out alarms.json --max 50000
"""

import asyncio
import json
import os
import time
import argparse
from datetime import datetime, timezone
from pathlib import Path
from playwright.async_api import async_playwright

# =========================
# 配置（请根据实际环境填写）
# =========================
BASE_URL   = "https://172.96.255.65:28001"
LOGIN_URL  = f"{BASE_URL}/uportal/framework/default.html"
ALARM_URL  = f"{BASE_URL}/uportal/framework/default.html#/home"  # 登录后默认首页（脚本会再嗅探API）
USERNAME   = "your_username"
PASSWORD   = "your_password"

# 登录页输入框/按钮选择器（按顺序尝试）
LOGIN_SELECTORS_TRY = [
    # 你环境里常用的一组
    {"user": 'input[name="username"]', "pass": 'input[name="password"]', "btn": 'button[type="submit"]'},
    # 备选
    {"user": '#username', "pass": '#password', "btn": '#loginBtn'},
    {"user": '.username', "pass": '.password', "btn": '.login-btn'},
    {"user": 'input[placeholder*="用户"]', "pass": 'input[placeholder*="密码"]', "btn": 'button'},
]

# 可选：页面上的刷新/查询按钮，用于触发一次表格请求以嗅探真实请求体
REFRESH_TRIGGERS = [
    'button:has-text("刷新")',
    'button:has-text("查询")',
    '.refresh',
    '[title*="刷新"]'
]

# 目标接口相对路径（一般为此）
API_PATH = "/api/fm-active/v1/activealarms/table"

# =========================

async def login_and_get_page(p):
    """打开浏览器 -> 登录 -> 进入首页"""
    browser = await p.chromium.launch(
        headless=True,
        args=['--ignore-certificate-errors', '--ignore-ssl-errors']
    )
    context = await browser.new_context(
        ignore_https_errors=True,
        extra_http_headers={
            "Accept": "application/json, text/plain, */*",
            "X-Requested-With": "XMLHttpRequest",
        }
    )
    page = await context.new_page()
    print(f"[INFO] 打开登录页: {LOGIN_URL}")
    await page.goto(LOGIN_URL, timeout=45_000)
    await page.wait_for_timeout(2000)

    # 依次尝试不同的选择器
    for sels in LOGIN_SELECTORS_TRY:
        try:
            await page.fill(sels["user"], USERNAME, timeout=5_000)
            await page.fill(sels["pass"], PASSWORD, timeout=5_000)
            await page.click(sels["btn"], timeout=5_000)
            await page.wait_for_timeout(4000)
            # 简单判断：URL 不再是登录页即可
            if "login" not in page.url.lower():
                print(f"[INFO] 登录成功，当前URL: {page.url}")
                break
        except Exception:
            continue

    # 确保进入首页（或任一能触发告警请求的页面）
    await page.goto(ALARM_URL, timeout=45_000)
    await page.wait_for_timeout(3000)
    return browser, context, page

async def sniff_request_body_once(page, timeout_ms=20_000):
    """嗅探一次表格请求，获取真实 POST 体"""
    print("[INFO] 开始嗅探表格请求体 ...")
    # 等待初始网络空闲
    try:
        await page.wait_for_load_state("networkidle", timeout=8_000)
    except Exception:
        pass
    await page.wait_for_timeout(1000)

    # 尝试点页面上的“刷新/查询”触发一次请求
    for sel in REFRESH_TRIGGERS:
        try:
            el = await page.query_selector(sel)
            if el:
                print(f"[INFO] 试图点击触发: {sel}")
                await el.click()
                await page.wait_for_timeout(1000)
                break
        except Exception:
            continue

    # 等待一个目标 POST 请求出现
    def _matcher(req):
        try:
            return req.method == "POST" and API_PATH in req.url
        except Exception:
            return False

    req = await page.wait_for_event("request", predicate=_matcher, timeout=timeout_ms)
    body_text = req.post_data
    headers = dict(req.headers)
    if not body_text:
        raise RuntimeError("未捕获到有效的 POST 请求体")
    print("[INFO] 已捕获真实请求体")
    return json.loads(body_text), headers

async def post_via_page(page, url, body):
    """在页面环境内用 fetch 调用（最接近前端的方式）"""
    js = """
    async (url, body) => {
      const r = await fetch(url, {
        method: 'POST',
        headers: {'Content-Type': 'application/json','Accept':'application/json, text/plain, */*','X-Requested-With':'XMLHttpRequest'},
        body: JSON.stringify(body)
      });
      const t = await r.text();
      try { return JSON.parse(t); } catch(e) { return {"__status__": r.status, "__raw_text__": t}; }
    }
    """
    return await page.evaluate(js, url, body)

async def fetch_all_alarms(page, base_body, base_url=BASE_URL, max_alarms=50_000):
    """根据嗅探到的 base_body 分页拉取全部告警（原样返回）"""
    api_url = f"{base_url}{API_PATH}"

    # 清理旧 queryid，逐页请求时会由后端返回新的 queryid
    body = dict(base_body)
    body.pop("queryid", None)

    # pagesize 若未给出，给个常用值
    pagesize = int(body.get("pagesize", 500))
    body["pagesize"] = pagesize
    page_no = 1
    queryid = None

    all_alarms = []
    page_responses = []  # 每页完整原始响应
    print(f"[INFO] 开始分页拉取，pagesize={pagesize}")

    while len(all_alarms) < max_alarms:
        # 更新分页与时间戳（大多数系统需要）
        body["page"] = page_no
        body["timestamp"] = datetime.now(timezone.utc).isoformat(timespec="milliseconds").replace("+00:00", "Z")
        if queryid:
            body["queryid"] = queryid

        data = await post_via_page(page, api_url, body)

        if not isinstance(data, dict):
            print(f"[WARN] 第{page_no}页返回非JSON，类型={type(data)}，停止。")
            break

        # 记录完整响应
        page_responses.append({
            "page": page_no,
            "response": data
        })

        alarms = data.get("alarms", [])
        if alarms is None:
            alarms = []

        # 尝试拿到 queryid（首轮或后续变更）
        if not queryid:
            queryid = data.get("queryid")

        count = len(alarms)
        print(f"[INFO] 第{page_no}页获取 {count} 条")
        if count == 0:
            break

        all_alarms.extend(alarms)

        # 停止条件：本页数 < pagesize（通常为最后一页）
        if count < pagesize:
            break

        # 下一页
        page_no += 1
        await asyncio.sleep(0.15)

    return {
        "fetched_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "base_url": base_url,
        "api_path": API_PATH,
        "sniffed_body": base_body,     # 为了可追溯/复现
        "total_alarms": len(all_alarms),
        "pages_fetched": len(page_responses),
        "pages": page_responses,       # 每页完整响应（包含所有顶层字段）
        "alarms": all_alarms           # 合并后的 alarms（保持原始字段）
    }

async def main(args):
    async with async_playwright() as p:
        browser, context, page = await login_and_get_page(p)
        try:
            base_body, sniff_headers = await sniff_request_body_once(page)
            result = await fetch_all_alarms(
                page,
                base_body=base_body,
                base_url=BASE_URL,
                max_alarms=args.max
            )
        finally:
            await browser.close()

    # 输出文件
    out_path = args.out
    if not out_path:
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        out_path = f"zte_active_alarms_raw_{ts}.json"

    Path(os.path.dirname(out_path) or ".").mkdir(parents=True, exist_ok=True)
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    print("==================================================")
    print(f"[OK] 已保存原始数据：{out_path}")
    print(f"[OK] 总告警数：{result['total_alarms']}，分页：{result['pages_fetched']} 页")
    print("  - JSON 顶层 keys：fetched_at / base_url / api_path / sniffed_body / total_alarms / pages_fetched / pages / alarms")
    print("  - pages：为每页完整响应（保持系统原样字段与值）")
    print("  - alarms：合并后的告警数组（每条为系统原始告警对象）")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="抓取当前活动告警原始数据（不做任何处理）")
    parser.add_argument("--out", type=str, default="", help="输出文件路径（默认：zte_active_alarms_raw_YYYYmmdd_HHMMSS.json）")
    parser.add_argument("--max", type=int, default=50000, help="最大抓取条数上限，默认 50000")
    args = parser.parse_args()
    asyncio.run(main(args))
