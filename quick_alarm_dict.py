#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速告警字典查看工具
快速获取并显示当前网管系统中的告警类型和代码
"""

import asyncio
from collections import Counter
from alarm_monitor import ZTEAlarmMonitor

async def quick_dump_alarms():
    """快速获取并打印告警字典"""
    print("🔍 快速告警字典查看工具")
    print("=" * 60)
    
    monitor = ZTEAlarmMonitor()
    
    try:
        # 登录
        print("正在登录网管系统...")
        if not await monitor.login_with_playwright():
            print("❌ 登录失败")
            return
        
        print("✅ 登录成功")
        
        # 获取告警数据 (较少数量，快速获取)
        print("正在获取告警数据...")
        raw_alarms = await monitor.fetch_all_current_alarms(pagesize=200, max_alarms=1000)
        
        if not raw_alarms:
            print("❌ 未获取到告警数据")
            return
        
        print(f"✅ 获取到 {len(raw_alarms)} 条告警数据")
        
        # 统计分析
        alarm_codes = Counter()
        alarm_titles = Counter()
        alarm_types = Counter()
        code_to_title = {}
        
        print("\n正在分析数据...")
        
        for raw_alarm in raw_alarms:
            # 提取字段值的辅助函数
            def get_field_value(field_data, default=''):
                if isinstance(field_data, dict):
                    return field_data.get('value', field_data.get('displayname', default))
                return str(field_data) if field_data is not None else default
            
            # 告警代码
            code = raw_alarm.get('alarmcode', '未知代码')
            
            # 告警标题
            title = get_field_value(raw_alarm.get('alarmtitle', ''), '')
            if not title:
                title = raw_alarm.get('codename', '未知标题')
            
            # 告警类型
            alarm_type = get_field_value(raw_alarm.get('alarmtypename', ''), '未知类型')
            
            # 统计
            alarm_codes[code] += 1
            alarm_titles[title] += 1
            alarm_types[alarm_type] += 1
            code_to_title[code] = title
        
        # 打印结果
        print("\n" + "="*80)
        print("📊 告警字典统计结果")
        print("="*80)
        
        print(f"\n📈 数据概览:")
        print(f"  总告警数: {len(raw_alarms)}")
        print(f"  唯一告警代码: {len(alarm_codes)}")
        print(f"  唯一告警标题: {len(alarm_titles)}")
        print(f"  唯一告警类型: {len(alarm_types)}")
        
        # 1. 告警代码 TOP 20
        print(f"\n🔢 告警代码统计 (TOP 20)")
        print("-" * 80)
        print(f"{'代码':<15} {'次数':<8} {'告警标题'}")
        print("-" * 80)
        for code, count in alarm_codes.most_common(20):
            title = code_to_title.get(code, '未知')
            print(f"{str(code):<15} {count:<8} {title}")
        
        # 2. 告警标题 TOP 20
        print(f"\n📋 告警标题统计 (TOP 20)")
        print("-" * 60)
        print(f"{'告警标题':<45} {'次数'}")
        print("-" * 60)
        for title, count in alarm_titles.most_common(20):
            print(f"{title:<45} {count}")
        
        # 3. 告警类型统计
        print(f"\n📂 告警类型统计")
        print("-" * 40)
        print(f"{'告警类型':<25} {'次数'}")
        print("-" * 40)
        for alarm_type, count in alarm_types.most_common():
            print(f"{alarm_type:<25} {count}")
        
        # 4. 生成关键词建议
        print(f"\n💡 关键词建议 (基于高频告警)")
        print("-" * 50)
        
        # 分析高频告警标题，提取可能的关键词
        keywords_suggestions = {
            "电源相关": [],
            "传输相关": [],
            "设备相关": [],
            "小区相关": [],
            "其他": []
        }
        
        for title, count in alarm_titles.most_common(30):
            title_lower = title.lower()
            
            if any(kw in title_lower for kw in ['电源', '掉电', '停电', '市电', '电池', '电压']):
                keywords_suggestions["电源相关"].append(f"{title} ({count}次)")
            elif any(kw in title_lower for kw in ['传输', '光纤', '光口', '链路', 'e1', 'eth', '端口']):
                keywords_suggestions["传输相关"].append(f"{title} ({count}次)")
            elif any(kw in title_lower for kw in ['rru', 'bbu', 'aau', 'du', 'cu', '设备', '板卡']):
                keywords_suggestions["设备相关"].append(f"{title} ({count}次)")
            elif any(kw in title_lower for kw in ['小区', 'cell', 'lte', 'nr', '退服']):
                keywords_suggestions["小区相关"].append(f"{title} ({count}次)")
            else:
                keywords_suggestions["其他"].append(f"{title} ({count}次)")
        
        for category, items in keywords_suggestions.items():
            if items:
                print(f"\n{category}:")
                for item in items[:5]:  # 只显示前5个
                    print(f"  - {item}")
        
        # 5. 当前ALARM_TYPES配置对比
        print(f"\n🔧 当前配置匹配分析")
        print("-" * 50)
        
        from config import ALARM_TYPES
        matched_count = 0
        total_count = len(raw_alarms)
        
        print("当前ALARM_TYPES配置:")
        for alarm_type_name, config in ALARM_TYPES.items():
            keywords = config.get("keywords", [])
            print(f"  {alarm_type_name}: {keywords}")
            
            # 统计匹配的告警数量
            type_matched = 0
            for raw_alarm in raw_alarms:
                alarm = monitor._parse_alarm_data(raw_alarm)
                if alarm and monitor.is_target_alarm(alarm):
                    type_matched += 1
            
        print(f"\n匹配统计:")
        print(f"  总告警数: {total_count}")
        print(f"  目标告警数: {type_matched}")
        print(f"  匹配率: {type_matched/total_count*100:.1f}%")
        
        print(f"\n✅ 快速分析完成！")
        print(f"💡 建议: 根据上述高频告警，考虑在ALARM_TYPES中添加相应关键词")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if hasattr(monitor, 'page') and monitor.page:
            await monitor.page.close()
        if hasattr(monitor, 'browser') and monitor.browser:
            await monitor.browser.close()

if __name__ == "__main__":
    asyncio.run(quick_dump_alarms())
