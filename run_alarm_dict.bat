@echo off
chcp 65001 >nul
echo ========================================
echo 告警字典分析工具
echo ========================================
echo.
echo 请选择运行模式:
echo 1. 快速分析 (获取1000条告警，快速查看)
echo 2. 完整分析 (获取20000条告警，生成详细报告)
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 正在运行快速分析...
    python quick_alarm_dict.py
    pause
) else if "%choice%"=="2" (
    echo.
    echo 正在运行完整分析...
    python dump_alarm_dict.py
    pause
) else if "%choice%"=="3" (
    exit
) else (
    echo 无效选择，请重新运行
    pause
)
