#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中兴网管告警监控系统
功能：
1. 自动登录中兴网管系统
2. 获取当前告警列表
3. 监控特定告警的持续时间
4. 自动发送邮件通知区县维修
"""

import asyncio
import json
import os
import time
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from typing import Dict, List, Optional
import requests
from playwright.async_api import async_playwright
import logging
import pandas as pd
from config import ZTE_CONFIG, MONITOR_CONFIG, EMAIL_CONFIG, ALARM_TYPES, LOG_CONFIG

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG['level']),
    format=LOG_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOG_CONFIG['file'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ZTEAlarmMonitor:
    def __init__(self):
        # 网管登录信息
        self.username = ZTE_CONFIG["username"]
        self.password = ZTE_CONFIG["password"]
        self.base_url = ZTE_CONFIG["base_url"]
        self.login_url = ZTE_CONFIG["login_url"]
        self.alarm_url = ZTE_CONFIG["alarm_url"]

        # Playwright相关
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.base_body = None  # 缓存真实请求体
        self.sniffed = False   # 是否已抓取过请求体
        self.real_headers = None  # 缓存真实请求头
        self.force_page_fetch = False  # 强制使用页面fetch
        self.max_consecutive_403 = 3  # 最大连续403次数
        self._403_cnt = 0  # 当前403计数
        self.seen_uids = set()  # 已见过的告警UID，用于识别新告警
        self.notify_on_new = True  # 是否对新出现的告警立即通知
        self.group_history = {}  # 组告警历史记录 group_id -> set(threshold)
        self.group_history_file = "group_history.json"  # 历史记录文件
        self.warm_up_hours = 8  # 冷启动窗口，只处理最近8小时的告警
        self.first_run_complete = False  # 标记是否完成首次运行

        # 告警监控配置
        self.check_interval = MONITOR_CONFIG["check_interval"]
        self.time_thresholds = MONITOR_CONFIG["time_thresholds"]
        self.non_assessment_hours = MONITOR_CONFIG["non_assessment_hours"]

        # 邮件配置
        self.smtp_server = EMAIL_CONFIG["smtp_server"]
        self.smtp_port = EMAIL_CONFIG["smtp_port"]
        self.use_ssl = EMAIL_CONFIG.get("use_ssl", False)
        self.email_user = EMAIL_CONFIG["sender_email"]
        self.email_password = EMAIL_CONFIG["sender_password"]
        self.recipients = EMAIL_CONFIG["recipients"]

        # 告警历史记录
        self.alarm_history = {}
        self.last_check_time = None
        
    async def login_with_playwright(self) -> bool:
        """使用Playwright登录并保持会话"""
        try:
            # 启动Playwright
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=['--ignore-certificate-errors', '--ignore-ssl-errors']
            )
            self.context = await self.browser.new_context(
                ignore_https_errors=True,
                extra_http_headers={
                    "Accept": "application/json, text/plain, */*",
                    "X-Requested-With": "XMLHttpRequest",
                }
            )
            self.page = await self.context.new_page()

            logger.info(f"正在访问登录页面: {self.login_url}")
            await self.page.goto(self.login_url)
            await self.page.wait_for_timeout(3000)

            # 填写登录信息
            selectors = ZTE_CONFIG["login_selectors"]

            try:
                await self.page.wait_for_selector(selectors["username_input"], timeout=10000)
                await self.page.fill(selectors["username_input"], self.username)
                await self.page.fill(selectors["password_input"], self.password)

                logger.info("正在登录...")
                await self.page.click(selectors["login_button"])
                await self.page.wait_for_timeout(5000)

                # 检查是否登录成功
                current_url = self.page.url
                logger.info(f"登录后URL: {current_url}")

                if "login" not in current_url.lower() and "error" not in current_url.lower():
                    logger.info("登录成功，Playwright会话已建立")

                    # 登录成功后立即导航到告警页面并抓取真实请求体
                    try:
                        await self.page.goto(self.alarm_url)
                        await self.page.wait_for_timeout(3000)

                        self.base_body = await self.sniff_real_payload_once()
                        if self.base_body:
                            # 删除旧的queryid，每轮重新生成
                            self.base_body.pop("queryid", None)
                            self.sniffed = True
                            logger.info("已缓存真实请求体格式")
                        else:
                            logger.warning("未能获取真实请求体")
                    except Exception as e:
                        logger.warning(f"抓取真实请求体失败: {e}")

                    return True
                else:
                    logger.error("登录失败")
                    return False

            except Exception as e:
                logger.error(f"登录过程失败: {e}")
                return False

        except Exception as e:
            logger.error(f"启动Playwright失败: {e}")
            return False

    async def _try_alternative_login(self, page):
        """尝试其他登录方式"""
        alternative_selectors = [
            {'user': '#username', 'pass': '#password', 'btn': '#loginBtn'},
            {'user': '.username', 'pass': '.password', 'btn': '.login-btn'},
            {'user': 'input[placeholder*="用户"]', 'pass': 'input[placeholder*="密码"]', 'btn': 'button'},
        ]

        for selectors in alternative_selectors:
            try:
                await page.fill(selectors['user'], self.username)
                await page.fill(selectors['pass'], self.password)
                await page.click(selectors['btn'])
                await page.wait_for_timeout(3000)
                break
            except:
                continue

    async def _navigate_to_alarm_page(self, page):
        """导航到告警页面"""
        try:
            # 尝试点击告警管理菜单
            alarm_menu_selectors = [
                'text="告警管理"',
                '[title="告警管理"]',
                'a:has-text("告警管理")',
                '.menu-item:has-text("告警管理")'
            ]

            for selector in alarm_menu_selectors:
                try:
                    await page.click(selector)
                    await page.wait_for_timeout(2000)
                    break
                except:
                    continue

            # 尝试点击当前告警
            current_alarm_selectors = [
                'text="当前告警"',
                '[title="当前告警"]',
                'a:has-text("当前告警")',
                '.menu-item:has-text("当前告警")'
            ]

            for selector in current_alarm_selectors:
                try:
                    await page.click(selector)
                    await page.wait_for_timeout(2000)
                    break
                except:
                    continue

        except Exception as e:
            logger.warning(f"菜单导航失败: {e}")
    
    async def get_current_alarms_from_page(self) -> List[Dict]:
        """直接从网页抓取当前告警列表"""
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(
                    headless=True,
                    args=['--ignore-certificate-errors', '--ignore-ssl-errors']
                )
                context = await browser.new_context(ignore_https_errors=True)
                page = await context.new_page()

                # 设置cookies
                if self.cookies:
                    await context.add_cookies([
                        {'name': name, 'value': value, 'domain': '172.96.255.65', 'path': '/'}
                        for name, value in self.cookies.items()
                    ])

                logger.info("正在访问告警页面抓取数据...")
                await page.goto(self.alarm_url)
                await page.wait_for_timeout(5000)  # 等待页面加载

                # 等待告警表格加载
                try:
                    await page.wait_for_selector('table', timeout=10000)
                except:
                    logger.warning("未找到告警表格，尝试其他方式...")

                # 尝试多种方式获取告警数据
                alarms = []

                # 方法1：查找表格数据
                try:
                    rows = await page.query_selector_all('tr')
                    logger.info(f"找到 {len(rows)} 行数据")

                    for i, row in enumerate(rows):
                        if i == 0:  # 跳过表头
                            continue

                        cells = await row.query_selector_all('td')
                        if len(cells) >= 4:  # 至少要有基本信息
                            alarm_data = {}

                            # 提取单元格文本
                            for j, cell in enumerate(cells):
                                text = await cell.inner_text()
                                alarm_data[f'column_{j}'] = text.strip()

                            # 尝试识别常见字段
                            if len(cells) >= 6:
                                alarm_data['基站名称'] = await cells[0].inner_text() if cells[0] else ''
                                alarm_data['告警内容'] = await cells[1].inner_text() if cells[1] else ''
                                alarm_data['发生时间'] = await cells[2].inner_text() if cells[2] else ''
                                alarm_data['告警级别'] = await cells[3].inner_text() if cells[3] else ''
                                alarm_data['设备位置'] = await cells[4].inner_text() if cells[4] else ''
                                alarm_data['扇区'] = await cells[5].inner_text() if cells[5] else ''

                            alarms.append(alarm_data)

                except Exception as e:
                    logger.warning(f"表格解析失败: {e}")

                # 方法2：查找特定的告警元素
                if not alarms:
                    try:
                        alarm_elements = await page.query_selector_all('.alarm-item, .alarm-row, [class*="alarm"]')
                        for element in alarm_elements:
                            text = await element.inner_text()
                            if text and len(text) > 10:  # 过滤掉空的或太短的文本
                                alarms.append({'告警内容': text, '发生时间': '', '基站名称': ''})
                    except Exception as e:
                        logger.warning(f"告警元素解析失败: {e}")

                await browser.close()

                if alarms:
                    logger.info(f"从网页成功抓取到 {len(alarms)} 条告警")
                    return alarms
                else:
                    logger.warning("未能从网页抓取到告警数据")
                    return []

        except Exception as e:
            logger.error(f"网页抓取告警失败: {e}")
            return []

    async def _post_json(self, url: str, body: Dict) -> Dict:
        """
        优先用 context.request.post；
        403 / 返回结构没有 alarms 时，自动回退到 page.evaluate(fetch)。
        连续 403 超过阈值后，本轮直接强制使用 page.evaluate。
        """
        # 如果已经被判定强制使用页面 fetch，就直接走 page.fetch
        if self.force_page_fetch:
            return await self._post_via_page_fetch(body)

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/plain, */*",
            "Referer": self.alarm_url,
            "Origin": ZTE_CONFIG['base_url'],
            "X-Requested-With": "XMLHttpRequest",
        }

        # 如果在 sniff 时把真实 headers 也抓到了，可以合并进去（注意别带 host/content-length）
        if self.real_headers:
            for k, v in self.real_headers.items():
                lk = k.lower()
                if lk not in ("host", "content-length", "connection"):
                    headers[k] = v

        try:
            resp = await self.context.request.post(
                url, data=json.dumps(body), headers=headers
            )
            status = resp.status
            text = await resp.text()
            try:
                data = json.loads(text)
            except Exception:
                data = {"__raw_text__": text}

            if status != 200 or not isinstance(data, dict) or "alarms" not in data:
                logger.warning(
                    f"context.request 返回异常，status={status}, keys={list(data.keys()) if isinstance(data, dict) else type(data)}"
                )

                if status == 403:
                    self._403_cnt += 1
                    if self._403_cnt >= self.max_consecutive_403:
                        logger.warning("连续403过多，本轮切换到页面内fetch模式")
                        self.force_page_fetch = True

                # 回退到页面内 fetch
                return await self._post_via_page_fetch(body)

            # 成功
            self._403_cnt = 0
            return data

        except Exception as e:
            logger.warning(f"context.request.post 异常，回退到 page.evaluate: {e}")
            return await self._post_via_page_fetch(body)

    def generate_alarm_uid(self, raw_alarm: Dict) -> str:
        """
        生成告警的唯一标识符
        使用多个字段组合确保唯一性，避免误判重复
        """
        # 尝试使用alarmkey（如果存在且不为空）
        alarm_key = raw_alarm.get('alarmkey', '')
        if alarm_key and alarm_key.strip():
            return f"key_{alarm_key}"

        # 否则使用多字段组合
        site_name = raw_alarm.get('ran_fm_alarm_site_name', {}).get('value', '')
        alarm_code = raw_alarm.get('alarmcode', '')
        object_type = raw_alarm.get('ran_fm_alarm_object_type', {}).get('value', '')
        object_id = raw_alarm.get('ran_fm_alarm_object_id', {}).get('value', '')
        position = raw_alarm.get('position', '')
        alarm_time = raw_alarm.get('alarmraisedtime', '') or raw_alarm.get('servertime', '')

        # 组合生成唯一ID
        uid_parts = [
            site_name,
            str(alarm_code),
            object_type,
            object_id,
            position,
            str(alarm_time)
        ]

        # 过滤空值并连接
        uid_parts = [part for part in uid_parts if part and str(part).strip()]
        return "_".join(uid_parts) if uid_parts else f"fallback_{hash(str(raw_alarm))}"

    def show_latest_alarms(self, raw_alarms: List[Dict], top_n: int = 10):
        """显示最新的N条告警"""
        if not raw_alarms:
            return

        # 按时间排序（优先使用servertime，其次alarmraisedtime）
        def get_time_key(alarm):
            return alarm.get('servertime', 0) or alarm.get('alarmraisedtime', 0)

        latest = sorted(raw_alarms, key=get_time_key, reverse=True)[:top_n]

        logger.info(f"最新 {len(latest)} 条告警:")
        for i, alarm in enumerate(latest, 1):
            # 调试：打印第一条告警的所有字段
            if i == 1:
                logger.debug(f"原始告警字段示例: {list(alarm.keys())[:10]}...")

            # 使用多重兜底获取字段值
            site_name = (self._get(alarm, 'ran_fm_alarm_site_name', 'value') or
                        self._get(alarm, 'ran_fm_alarm_site_name', 'displayname') or
                        alarm.get('mename', ''))

            alarm_title = (self._get(alarm, 'alarmtitle', 'value') or
                          self._get(alarm, 'alarmtitle', 'displayname') or
                          alarm.get('codename', ''))

            # 移除级别显示

            alarm_time = alarm.get('servertime', 0) or alarm.get('alarmraisedtime', 0)

            # 如果还是空值，尝试其他字段
            if not site_name:
                site_name = alarm.get('componentdn', '未知站点')
            if not alarm_title:
                alarm_title = alarm.get('additionaltext', '未知告警')

            # 转换时间戳为可读格式
            if alarm_time:
                try:
                    from datetime import datetime
                    time_str = datetime.fromtimestamp(int(alarm_time)/1000).strftime('%m-%d %H:%M')
                except:
                    time_str = str(alarm_time)
            else:
                time_str = '未知时间'

            # 截断过长的字段
            if len(site_name) > 25:
                site_name = site_name[:22] + "..."
            if len(alarm_title) > 30:
                alarm_title = alarm_title[:27] + "..."

            logger.info(f"  {i:2d}. {site_name}: {alarm_title} ({time_str})")

    def _get(self, d, *path, default=None):
        """安全获取嵌套字典的值"""
        x = d
        for p in path:
            if not isinstance(x, dict):
                return default
            x = x.get(p, default)
        return x

    def classify_role(self, raw):
        """分类告警角色：根因、次根因、衍生、独立、未关联"""
        # 独立优先
        unrel = self._get(raw, 'aax_unrelationflag', 'value')
        if unrel == '1':
            return '独立'

        flag = str(raw.get('relationflag', ''))
        name = (raw.get('relationflagname') or '').strip()

        if flag == '3' or name == '根源':
            return '根因'
        if flag == '2' or name == '次根源':
            return '次根因'

        # 有父信息就认为是衍生
        parents = self.get_parent_alarmkeys(raw)
        if parents:
            return '衍生'

        return '未关联'

    def get_alarm_uid(self, raw):
        """获取告警唯一标识"""
        return raw.get('alarmkey') or str(raw.get('id'))

    def get_parent_alarmkeys(self, raw):
        """
        解析 parentinfo.relation_YYYY_MM，返回可能存在的父 alarmkey 列表
        """
        parentinfo = raw.get('parentinfo') or {}
        # 找出任何以 relation_ 开头的 key（一般是 relation_2025_07）
        rel_key = next((k for k in parentinfo if k.startswith('relation_')), None)
        if not rel_key:
            return []

        parents = []
        parent_data = parentinfo[rel_key]
        if isinstance(parent_data, list):
            for item in parent_data:
                if isinstance(item, str):
                    # 取第一个 @ 之前的 alarmkey
                    alarmkey = item.split('@', 1)[0]
                    parents.append(alarmkey)
        return parents

    def get_group_id(self, raw):
        """获取告警组ID"""
        role = self.classify_role(raw)
        my_key = self.get_alarm_uid(raw)

        if role in ('根因', '独立', '未关联'):
            return my_key  # 自己就是组ID

        # 衍生/次根因：找最顶层父 alarmkey（如果拿不到，就用自己兜底）
        parents = self.get_parent_alarmkeys(raw)
        return parents[0] if parents else my_key

    def build_groups(self, alarms):
        """将告警按关联关系分组"""
        groups = {}
        for a in alarms:
            raw = a.get('原始数据', {})
            role = self.classify_role(raw)
            gid = self.get_group_id(raw)
            uid = self.get_alarm_uid(raw)

            g = groups.setdefault(gid, {
                'group_id': gid,
                'root': None,
                'alarms': [],
                'roles': {'根因': [], '次根因': [], '衍生': [], '独立': [], '未关联': []},
                'first_raised': None,
                'last_changed': None,
                'last_servertime': None,

            })

            # 维护角色列表
            g['alarms'].append(a)
            g['roles'][role].append(a)

            # 移除级别统计，不再需要

            # 找根因（如果有多条，取第一条）
            if role == '根因' and g['root'] is None:
                g['root'] = a

            # 维护时间（用于计算持续）- 使用alarmraisedtime
            raised_dt = self.get_alarm_raised_dt(raw)
            if raised_dt:
                if g['first_raised'] is None or raised_dt < g['first_raised']:
                    g['first_raised'] = raised_dt

            # 变更 / 服务器时间（取最大）
            try:
                st_ms = int(raw.get('servertime', 0))
                if st_ms:
                    if g['last_servertime'] is None or st_ms > g['last_servertime']:
                        g['last_servertime'] = st_ms
            except:
                pass

        return groups

    def should_send_group(self, gid, duration_hours):
        """判断组告警是否应该发送通知"""
        # group_history 里存储每个 gid 已经发过的阈值
        sent = self.group_history.setdefault(gid, set())
        eligible = [t for t in self.time_thresholds if duration_hours >= t]
        if not eligible:
            return False, ""
        t = max(eligible)
        if t in sent:
            return False, ""
        sent.add(t)
        return True, f"{t}小时"

    def get_alarm_raised_dt(self, raw):
        """从原始数据获取告警发生时间"""
        ts = raw.get('alarmraisedtime')
        if not ts:
            return None
        try:
            # 毫秒时间戳 -> datetime（本地时区）
            from datetime import timezone, timedelta
            return datetime.fromtimestamp(int(ts)/1000, tz=timezone(timedelta(hours=8)))
        except:
            return None

    def calculate_duration_since(self, start_dt):
        """计算从指定时间到现在的持续时间（小时）"""
        if not start_dt:
            return 0
        now = datetime.now(start_dt.tzinfo) if start_dt.tzinfo else datetime.now()
        return (now - start_dt).total_seconds() / 3600.0

    def load_group_history(self):
        """加载组告警历史记录"""
        try:
            if os.path.exists(self.group_history_file):
                with open(self.group_history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 转换为set格式
                    return {gid: set(thresholds) for gid, thresholds in data.items()}
            return {}
        except Exception as e:
            logger.warning(f"加载历史记录失败: {e}")
            return {}

    def save_group_history(self):
        """保存组告警历史记录"""
        try:
            # 转换为list格式以便JSON序列化
            data = {gid: list(thresholds) for gid, thresholds in self.group_history.items()}
            with open(self.group_history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"保存历史记录失败: {e}")

    def is_recent_alarm(self, raw, hours=None):
        """判断告警是否在指定时间窗口内"""
        if hours is None:
            hours = self.warm_up_hours

        raised_dt = self.get_alarm_raised_dt(raw)
        if not raised_dt:
            return False

        now = datetime.now(raised_dt.tzinfo) if raised_dt.tzinfo else datetime.now()
        return (now - raised_dt).total_seconds() / 3600.0 <= hours

    def get_station_name(self, a):
        """获取站点名称，多重兜底"""
        return (a.get('基站名称') or
                self._get(a.get('原始数据', {}), 'ran_fm_alarm_site_name', 'displayname') or
                self._get(a.get('原始数据', {}), 'mename') or
                '未知站点')

    def identify_new_alarms(self, raw_alarms: List[Dict]) -> List[Dict]:
        """识别新出现的告警"""
        new_alarms = []

        for raw_alarm in raw_alarms:
            uid = self.generate_alarm_uid(raw_alarm)
            if uid not in self.seen_uids:
                self.seen_uids.add(uid)
                new_alarms.append(raw_alarm)

        return new_alarms

    async def _post_via_page_fetch(self, body: Dict) -> Dict:
        """使用页面内fetch方法"""
        js_code = """
        (body) => fetch('/api/fm-active/v1/activealarms/table', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(body)
        }).then(r => r.json())
        """
        data = await self.page.evaluate(js_code, body)
        if isinstance(data, dict) and "alarms" in data:
            return data
        logger.warning(f"page.fetch 返回异常，keys={list(data.keys()) if isinstance(data, dict) else type(data)}")
        return data

    async def sniff_real_payload_once(self, timeout: float = 15000) -> Dict:
        """
        只抓一次 /activealarms/table 的真实 POST 体，不注册长期监听器。
        """
        try:
            logger.info("开始监听网络请求，获取真实API调用格式...")

            # 等待页面自动发送请求
            await self.page.wait_for_load_state("networkidle")
            await self.page.wait_for_timeout(3000)

            # 尝试手动触发请求
            try:
                refresh_selectors = [
                    'button:has-text("刷新")', 'button:has-text("查询")',
                    '.refresh', '[title*="刷新"]'
                ]

                for selector in refresh_selectors:
                    try:
                        element = await self.page.query_selector(selector)
                        if element:
                            logger.info(f"点击刷新按钮触发请求: {selector}")
                            await element.click()
                            break
                    except:
                        continue
            except Exception as e:
                logger.warning(f"触发刷新失败: {e}")

            # 使用wait_for_event一次性获取请求
            request = await self.page.wait_for_event(
                "request",
                lambda r: r.method == "POST" and "/api/fm-active/v1/activealarms/table" in r.url,
                timeout=timeout
            )

            body_text = request.post_data
            if body_text:
                # 保存 headers（可选）
                try:
                    self.real_headers = dict(request.headers)
                except Exception:
                    self.real_headers = None

                body = json.loads(body_text)
                logger.info("成功捕获真实请求体")
                return body
            else:
                logger.warning("请求体为空")
                return None

        except Exception as e:
            logger.error(f"抓取真实请求体失败: {e}")
            return None

    async def fetch_all_current_alarms(self, pagesize: int = 500, max_alarms: int = 50000) -> List[Dict]:
        """
        使用真实请求体格式获取所有告警数据
        """
        try:
            if not self.page:
                logger.error("Playwright页面未建立，请先登录")
                return []

            # 如果还没有缓存的请求体，说明登录时抓取失败，尝试重新抓取
            if not self.base_body or not self.sniffed:
                logger.warning("缺少真实请求体，尝试重新获取...")
                await self.page.goto(self.alarm_url)
                await self.page.wait_for_timeout(3000)

                self.base_body = await self.sniff_real_payload_once()
                if not self.base_body:
                    logger.error("无法获取真实请求体")
                    return []

                self.base_body.pop("queryid", None)
                self.sniffed = True
                logger.info("已重新缓存真实请求体格式")

            start_time = time.time()

            # 复制并修改请求体
            import copy
            from datetime import datetime, timezone

            body = copy.deepcopy(self.base_body)
            body["pagesize"] = pagesize
            body["page"] = 1
            # 删除旧的queryid，让后端重新生成
            body.pop("queryid", None)

            all_alarms = []
            queryid = None

            # 使用安全的POST方法，自动回退
            api_url = f"{ZTE_CONFIG['base_url']}/api/fm-active/v1/activealarms/table"

            page_num = 1
            while len(all_alarms) < max_alarms:
                body["page"] = page_num
                body["timestamp"] = datetime.now(timezone.utc).isoformat(timespec="milliseconds").replace("+00:00", "Z")

                if queryid:
                    body["queryid"] = queryid

                data = await self._post_json(api_url, body)

                if not isinstance(data, dict):
                    logger.warning(f"第{page_num}页返回非JSON: {type(data)}")
                    break

                if "alarms" not in data:
                    logger.warning(f"第{page_num}页没有 alarms 字段，keys={list(data.keys())}")
                    break

                raw_alarms = data['alarms']
                current_count = len(raw_alarms)
                logger.debug(f"第{page_num}页获取 {current_count} 条")

                # 第一次请求时获取queryid
                if not queryid:
                    queryid = data.get('queryid', '')

                if not raw_alarms:
                    break

                # 转换为标准格式
                for raw_alarm in raw_alarms:
                    alarm = self._parse_alarm_data(raw_alarm)
                    if alarm:
                        all_alarms.append(alarm)

                # 最可靠的停止条件：返回条数 < pagesize
                if current_count < pagesize:
                    break

                # 如果已达到最大限制，退出循环
                if len(all_alarms) >= max_alarms:
                    break

                page_num += 1

                # 避免请求过快
                await self.page.wait_for_timeout(200)

            elapsed_time = time.time() - start_time
            if all_alarms:
                logger.info(f"本轮采集：共 {len(all_alarms)} 条，耗时 {elapsed_time:.2f} 秒")
                return all_alarms
            else:
                logger.warning("未获取到告警数据")
                return []

        except Exception as e:
            logger.error(f"获取告警列表失败: {e}")
            return []

    async def fetch_alarm_rows_via_page(self, max_alarms: int = 50000) -> List[Dict]:
        """
        获取告警数据的主入口，使用正确的分页机制
        """
        return await self.fetch_all_current_alarms(pagesize=500, max_alarms=max_alarms)

    def _parse_alarm_data(self, raw_alarm: Dict) -> Dict:
        """解析原始告警数据为标准格式"""
        try:
            # 提取字段值的辅助函数
            def get_field_value(field_data, default=''):
                if isinstance(field_data, dict) and 'value' in field_data:
                    return field_data['value']
                elif isinstance(field_data, dict) and 'displayname' in field_data:
                    return field_data['displayname']
                else:
                    return str(field_data) if field_data is not None else default

            # 转换时间戳为标准格式
            alarm_time = ''
            if 'alarmraisedtime' in raw_alarm:
                timestamp = raw_alarm['alarmraisedtime']
                if isinstance(timestamp, (int, float)):
                    # 转换毫秒时间戳为日期时间
                    from datetime import datetime
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    alarm_time = dt.strftime('%Y-%m-%d %H:%M:%S')

            # 构建标准告警数据
            alarm = {
                '告警ID': str(raw_alarm.get('id', '')),
                '基站名称': get_field_value(raw_alarm.get('ran_fm_alarm_site_name', '')),
                '告警对象名称': get_field_value(raw_alarm.get('ran_fm_alarm_object_name', '')),
                '告警内容': get_field_value(raw_alarm.get('alarmtitle', raw_alarm.get('codename', ''))),
                '故障原因': raw_alarm.get('reasonname', ''),
                '发生时间': alarm_time,
                '告警级别': raw_alarm.get('perceivedseverityname', ''),
                '设备位置': get_field_value(raw_alarm.get('ran_fm_alarm_location', '')),
                '扇区': get_field_value(raw_alarm.get('ran_fm_alarm_object_id', '')),
                '告警类型': raw_alarm.get('alarmtypename', ''),
                '详细信息': raw_alarm.get('additionaltext', ''),
                '确认状态': raw_alarm.get('ackstatename', ''),
                '清除状态': raw_alarm.get('clearstatename', ''),
                '原始数据': raw_alarm  # 保留原始数据以备调试
            }

            return alarm

        except Exception as e:
            logger.error(f"解析告警数据失败: {e}")
            return None

    async def get_current_alarms(self) -> List[Dict]:
        """获取当前告警列表"""
        # 优先使用页面内fetch方式
        return await self.fetch_alarm_rows_via_page()
    
    def calculate_alarm_duration(self, alarm_time: str) -> float:
        """
        计算告警持续时间（排除0-6点）
        返回小时数
        """
        try:
            # 尝试多种时间格式
            time_formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y/%m/%d %H:%M:%S",
                "%Y-%m-%d %H:%M",
                "%Y/%m/%d %H:%M",
                "%m/%d/%Y %H:%M:%S",
                "%d/%m/%Y %H:%M:%S"
            ]

            start_time = None
            for fmt in time_formats:
                try:
                    start_time = datetime.strptime(alarm_time, fmt)
                    break
                except ValueError:
                    continue

            if start_time is None:
                logger.error(f"无法解析告警时间格式: {alarm_time}")
                return 0

            current_time = datetime.now()
            total_hours = 0
            check_time = start_time

            # 按小时计算，排除非考核时间
            while check_time < current_time:
                next_hour = check_time + timedelta(hours=1)
                if next_hour > current_time:
                    next_hour = current_time

                # 检查这个小时是否在考核时间内（6:00-24:00）
                start_hour, end_hour = self.non_assessment_hours
                if not (start_hour <= check_time.hour < end_hour):
                    duration = (next_hour - check_time).total_seconds() / 3600
                    total_hours += duration

                check_time = next_hour

            return total_hours

        except Exception as e:
            logger.error(f"计算告警持续时间失败: {e}")
            return 0
    
    def is_target_alarm(self, alarm: Dict) -> bool:
        """判断是否为需要监控的特定告警类型"""
        # 检查告警内容和详细信息
        alarm_content = alarm.get('告警内容', '').lower()
        alarm_detail = alarm.get('详细信息', '').lower()
        alarm_type = alarm.get('告警类型', '').lower()

        search_text = f"{alarm_content} {alarm_detail} {alarm_type}"

        for alarm_type_name, config in ALARM_TYPES.items():
            for keyword in config["keywords"]:
                if keyword.lower() in search_text:
                    # 只在DEBUG级别记录，避免日志刷屏
                    # logger.debug(f"发现目标告警类型 '{alarm_type_name}': {alarm.get('告警内容', '')}")
                    return True
        return False

    def should_send_notification(self, alarm_id: str, duration_hours: float) -> tuple[bool, str]:
        """
        判断是否需要发送通知
        返回 (是否发送, 通知类型)
        只发送当前未通知过的最高阈值
        """
        # 找到所有满足条件的阈值
        eligible_thresholds = [t for t in self.time_thresholds if duration_hours >= t]
        if not eligible_thresholds:
            return False, ""

        # 取最高阈值
        max_threshold = max(eligible_thresholds)

        # 检查是否已经发送过这个阈值的通知
        key = f"{alarm_id}_{max_threshold}h"
        if key not in self.alarm_history:
            self.alarm_history[key] = True
            return True, f"{max_threshold}小时"

        return False, ""
    
    def send_group_email(self, group, duration_hours: float, notification_type: str):
        """发送组告警邮件"""
        try:
            alarms = group['alarms']
            root = group['root']
            gid = group['group_id']

            # 标题
            base_station = root.get('基站名称') if root else alarms[0].get('基站名称', '未知基站')
            root_title = root.get('告警内容') if root else alarms[0].get('告警内容', '未知告警')
            count = len(alarms)

            urgency = "【紧急】" if duration_hours >= 8 else "【重要】" if duration_hours >= 4 else "【提醒】"
            subject = f"{urgency}[组告警] {base_station} - {root_title} - 共{count}条 - 持续{notification_type}"

            # 明细表
            rows = []
            for a in alarms:
                raw = a.get('原始数据', {})
                role = self.classify_role(raw)
                rows.append(
                    f"{a.get('发生时间','')}  "
                    f"{a.get('告警内容','')}  "
                    f"{a.get('告警对象名称','')}  "
                    f"{role}  "
                    f"{a.get('清除状态','')}/{a.get('确认状态','')}"
                )

            body = f"""
菏泽联通网管【组告警】通知

事件组ID: {gid}
站点（局向）: {base_station}
根因告警: {root_title if root else '未识别到根因（可能全部是衍生）'}
组内总条数: {count}
持续时间: {duration_hours:.1f} 小时（{notification_type}）

———————————————— 组内告警明细 ————————————————
时间 | 告警内容 | 告警对象 | 角色 | 清除/确认
{chr(10).join(rows)}
———————————————————————————————————————————

请相关区县及时处理！

发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
（本邮件由系统按"组"合并发送，避免多次重复告警）
"""

            # 发送邮件
            self._send_email(subject, body)
            logger.info(f"组告警邮件已发送: {base_station} - 共{count}条 - 持续{notification_type}")
            return True

        except Exception as e:
            logger.error(f"发送组告警邮件失败: {e}")
            return False

    def _send_email(self, subject: str, body: str):
        """发送邮件的底层方法"""
        msg = MIMEMultipart()
        msg['From'] = self.email_user
        msg['To'] = ", ".join(self.recipients)  # 统一用 self.recipients
        msg['Subject'] = subject
        msg.attach(MIMEText(body, 'plain', 'utf-8'))

        if self.use_ssl:
            server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
        else:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()

        server.login(self.email_user, self.email_password)
        server.send_message(msg)
        server.quit()

    def send_email_notification(self, alarm_info: Dict, duration_hours: float, notification_type: str):
        """发送邮件通知"""
        try:
            # 构建邮件
            msg = MIMEMultipart()
            msg['From'] = self.email_user
            msg['To'] = ", ".join(self.recipients)

            # 根据持续时间设置邮件主题的紧急程度
            urgency = "【紧急】" if duration_hours >= 8 else "【重要】" if duration_hours >= 4 else "【提醒】"
            base_station = alarm_info.get('基站名称', '未知基站')
            alarm_content = alarm_info.get('告警内容', '未知告警')
            msg['Subject'] = f"{urgency}告警通知 - {base_station} - {alarm_content} - 持续{notification_type}"

            # 提取告警信息
            alarm_fields = {
                '基站名称': alarm_info.get('基站名称', '未知'),
                '告警对象': alarm_info.get('告警对象名称', '未知'),
                '扇区': alarm_info.get('扇区', '未知'),
                '设备位置': alarm_info.get('设备位置', '未知'),
                '告警内容': alarm_info.get('告警内容', '未知'),
                '故障原因': alarm_info.get('故障原因', '未知'),
                '发生时间': alarm_info.get('发生时间', '未知'),
                '告警级别': alarm_info.get('告警级别', '未知'),
                '告警类型': alarm_info.get('告警类型', '未知'),
                '确认状态': alarm_info.get('确认状态', '未知'),
                '清除状态': alarm_info.get('清除状态', '未知')
            }

            # 邮件内容
            body = f"""
菏泽联通网管告警通知

告警详情：
基站名称: {alarm_fields['基站名称']}
告警对象: {alarm_fields['告警对象']}
扇区: {alarm_fields['扇区']}
设备位置: {alarm_fields['设备位置']}
告警内容: {alarm_fields['告警内容']}
故障原因: {alarm_fields['故障原因']}
告警级别: {alarm_fields['告警级别']}
告警类型: {alarm_fields['告警类型']}
发生时间: {alarm_fields['发生时间']}
持续时间: {duration_hours:.1f} 小时 ({notification_type})
确认状态: {alarm_fields['确认状态']}
清除状态: {alarm_fields['清除状态']}

请相关区县及时处理！

注意：此告警已持续{notification_type}，请优先处理。

发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---
菏泽联通告警监控系统自动发送
            """

            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # 发送邮件
            if self.use_ssl:
                # 使用SSL连接
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                # 使用普通SMTP + STARTTLS
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()

            server.login(self.email_user, self.email_password)
            server.send_message(msg)
            server.quit()

            logger.info(f"邮件通知已发送: {base_station} - 持续{notification_type}")

        except Exception as e:
            logger.error(f"发送邮件失败: {e}")
    
    async def monitor_alarms(self):
        """主监控循环"""
        logger.info("开始监控告警...")

        # 打印当前的目标告警配置
        logger.info("当前目标告警配置 (ALARM_TYPES):")
        for alarm_type_name, config in ALARM_TYPES.items():
            keywords = config.get("keywords", [])
            priority = config.get("priority", "unknown")
            logger.info(f"  - {alarm_type_name} ({priority}): {keywords}")

        # 加载历史记录
        self.group_history = self.load_group_history()
        is_cold_start = len(self.group_history) == 0

        if is_cold_start:
            logger.info("检测到首次启动，本轮将记录所有告警状态但不发送邮件，下轮开始正常监控")

        while True:
            try:
                # 每轮开始前复位
                self.force_page_fetch = False
                self._403_cnt = 0
                sent_this_round = set()  # 本轮去重

                current_time = datetime.now()
                logger.info(f"开始第{current_time.strftime('%Y-%m-%d %H:%M:%S')}轮检查")

                # 获取当前告警
                alarms = await self.get_current_alarms()

                if not alarms:
                    logger.info("未获取到告警数据")
                    # 不要频繁重新登录，可能是API接口问题
                    logger.info("建议运行 python page_analyzer.py 来分析页面结构")
                    await asyncio.sleep(300)  # 等待5分钟后重试
                    continue

                # 保存当前告警数据供Web界面使用
                self.current_alarms = alarms

                # 获取原始告警数据用于新告警识别
                raw_alarms = await self.fetch_all_current_alarms(pagesize=500, max_alarms=50000)

                # 调试：第一次运行时输出数据结构样例（简化版）
                if raw_alarms and not hasattr(self, '_debug_printed'):
                    self._debug_printed = True
                    sample_alarm = raw_alarms[0]
                    logger.info(f"数据获取正常，共{len(sample_alarm)}个字段")

                # 识别新出现的告警
                if raw_alarms:
                    new_alarms = self.identify_new_alarms(raw_alarms)
                    if new_alarms:
                        logger.info(f"发现 {len(new_alarms)} 条新告警")

                        # 显示最新告警
                        self.show_latest_alarms(new_alarms, top_n=5)

                        # 如果启用了新告警通知
                        if self.notify_on_new:
                            for raw_alarm in new_alarms:
                                # 转换为标准格式检查是否为目标告警
                                alarm = self._parse_alarm_data(raw_alarm)
                                if alarm and self.is_target_alarm(alarm):
                                    logger.info(f"新发现目标告警: {alarm.get('基站名称', '')} - {alarm.get('告警内容', '')}")

                    # 显示最新的所有告警（不限新旧）
                    logger.info("当前最新告警概览:")
                    self.show_latest_alarms(raw_alarms, top_n=3)

                # 使用组告警逻辑
                # 先筛选目标告警
                target_alarms = [alarm for alarm in alarms if self.is_target_alarm(alarm)]

                if not target_alarms:
                    logger.info("本轮未发现目标告警")
                else:
                    # 按关联关系分组
                    groups = self.build_groups(target_alarms)
                    notifications_sent = 0
                    notification_summary = {}

                    logger.info(f"目标告警分组结果: 共{len(target_alarms)}条告警，分为{len(groups)}个组")

                    # 收集重要组信息，避免重复显示
                    important_groups = []
                    processed_groups = set()  # 防止重复处理同一组

                    for gid, group in groups.items():
                        # 防止重复处理
                        if gid in processed_groups:
                            continue
                        processed_groups.add(gid)

                        # 显示组信息
                        count = len(group['alarms'])
                        roles_count = {role: len(alarms) for role, alarms in group['roles'].items() if alarms}

                        # 只收集有多条告警或有关联关系的组
                        if count > 1 or any(role in ['根因', '次根因', '衍生'] for role in roles_count.keys()):
                            # 获取组的代表性信息
                            representative_alarm = group['root'] if group['root'] else group['alarms'][0]
                            station_name = self.get_station_name(representative_alarm)
                            alarm_content = representative_alarm.get('告警内容', '未知告警')

                            # 如果站点名太长，截断显示
                            if len(station_name) > 30:
                                station_display = station_name[:27] + "..."
                            else:
                                station_display = station_name

                            roles_str = ", ".join([f"{role}:{count}" for role, count in roles_count.items()])
                            important_groups.append((station_display, alarm_content, count, roles_str))

                    # 统一显示重要组信息（限制显示数量）
                    if important_groups:
                        display_count = min(10, len(important_groups))
                        logger.info(f"重要告警组 (显示前{display_count}个):")
                        for i, (station, content, count, roles) in enumerate(important_groups[:display_count], 1):
                            logger.info(f"  {i:2d}. [{station}] {content}: {count}条 ({roles})")

                        if len(important_groups) > 10:
                            logger.info(f"  ... 还有 {len(important_groups) - 10} 个组未显示")

                    # 处理告警通知
                    processed_groups = set()  # 重置，用于通知处理

                    for gid, group in groups.items():
                        # 防止重复处理
                        if gid in processed_groups:
                            continue
                        processed_groups.add(gid)

                        # 计算组的持续时间
                        start_time = group['first_raised']
                        if start_time:
                            duration = self.calculate_duration_since(start_time)

                            # 首次启动：只记录状态，不发送邮件
                            if is_cold_start:
                                # 记录所有当前应该触发的阈值到历史记录
                                eligible_thresholds = [t for t in self.time_thresholds if duration >= t]
                                if eligible_thresholds:
                                    self.group_history.setdefault(gid, set()).update(eligible_thresholds)
                                    logger.debug(f"首次启动记录组[{gid[:20]}...]: 持续{duration:.1f}小时, 记录阈值{eligible_thresholds}")
                                continue

                            # 正常运行：判断是否需要发送组通知
                            should_send, notification_type = self.should_send_group(gid, duration)

                            if should_send:
                                # 本轮去重检查
                                threshold = int(notification_type.replace('小时', ''))
                                round_key = (gid, threshold)

                                if round_key in sent_this_round:
                                    continue

                                # 发送邮件
                                success = self.send_group_email(group, duration, notification_type)

                                if success:
                                    sent_this_round.add(round_key)
                                    notifications_sent += 1

                                    # 统计通知类型
                                    if notification_type not in notification_summary:
                                        notification_summary[notification_type] = 0
                                    notification_summary[notification_type] += 1

                                    station_name = self.get_station_name(group['root']) if group['root'] else self.get_station_name(group['alarms'][0])
                                    logger.info(f"发送组通知: {station_name} - 共{count}条 - 持续{notification_type} ({duration:.1f}小时)")
                                else:
                                    # 发送失败，从历史记录中移除该阈值
                                    self.group_history.get(gid, set()).discard(threshold)
                        else:
                            logger.warning(f"组[{gid[:20]}...]无法确定开始时间，跳过通知")

                # 移除级别分布统计

                if is_cold_start:
                    # 首次启动的日志
                    recorded_groups = len([gid for gid in self.group_history.keys() if self.group_history[gid]])
                    logger.info(f"首次启动完成 - 总告警:{len(alarms)}, 目标告警:{len(target_alarms)}, 记录组状态:{recorded_groups}")

                    # 显示目标告警类型分布
                    if target_alarms:
                        logger.info(f"发现目标告警类型分布:")
                        alarm_type_count = {}
                        for alarm in target_alarms:
                            alarm_content = alarm.get('告警内容', '未知')
                            # 简单分类
                            if 'rru' in alarm_content.lower() or '小区' in alarm_content:
                                alarm_type = 'RRU/小区故障'
                            elif '电源' in alarm_content or '掉电' in alarm_content:
                                alarm_type = '电源故障'
                            elif '光' in alarm_content:
                                alarm_type = '传输故障'
                            else:
                                alarm_type = '其他故障'

                            alarm_type_count[alarm_type] = alarm_type_count.get(alarm_type, 0) + 1

                        type_summary = ", ".join([f"{k}({v}条)" for k, v in alarm_type_count.items()])
                        logger.info(f"  {type_summary}")

                    logger.info("下轮开始将正常发送告警通知")
                else:
                    # 正常运行的日志
                    logger.info(f"本轮检查完成 - 总告警:{len(alarms)}, 目标告警:{len(target_alarms)}, 发送组通知:{notifications_sent}")

                    # 显示通知汇总
                    if notification_summary:
                        summary_str = ", ".join([f"{k}({v}组)" for k, v in notification_summary.items()])
                        logger.info(f"本轮触发组通知: {summary_str}")

                    # 显示目标告警类型分布
                    if target_alarms:
                        logger.info(f"本轮发现目标告警类型分布:")
                        alarm_type_count = {}
                        for alarm in target_alarms:
                            alarm_content = alarm.get('告警内容', '未知')
                            # 简单分类
                            if 'rru' in alarm_content.lower() or '小区' in alarm_content:
                                alarm_type = 'RRU/小区故障'
                            elif '电源' in alarm_content or '掉电' in alarm_content:
                                alarm_type = '电源故障'
                            elif '光' in alarm_content:
                                alarm_type = '传输故障'
                            else:
                                alarm_type = '其他故障'

                            alarm_type_count[alarm_type] = alarm_type_count.get(alarm_type, 0) + 1

                        type_summary = ", ".join([f"{k}({v}条)" for k, v in alarm_type_count.items()])
                        logger.info(f"  {type_summary}")
                    else:
                        logger.info("本轮未发现目标告警")

                # 保存历史记录
                self.save_group_history()

                # 第一轮后关闭冷启动模式
                if is_cold_start:
                    is_cold_start = False
                    self.first_run_complete = True

                self.last_check_time = current_time

            except Exception as e:
                logger.error(f"监控过程出错: {e}")
                # 出错时等待更长时间
                await asyncio.sleep(60)
                continue

            # 等待下次检查
            logger.info(f"等待{self.check_interval}秒后进行下次检查...")
            await asyncio.sleep(self.check_interval)

    async def cleanup(self):
        """清理Playwright资源"""
        try:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

async def main():
    monitor = ZTEAlarmMonitor()

    try:
        # 登录获取cookies
        if await monitor.login_with_playwright():
            # 开始监控
            await monitor.monitor_alarms()
        else:
            logger.error("登录失败，程序退出")
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
    finally:
        # 清理资源
        await monitor.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
