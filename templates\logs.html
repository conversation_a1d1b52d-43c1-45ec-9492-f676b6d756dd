{% extends "base.html" %}

{% block title %}系统日志 - 告警监控系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-file-text"></i> 系统日志</h2>
    <div>
        <button class="btn btn-outline-primary me-2" onclick="refreshLogs()">
            <i class="bi bi-arrow-clockwise"></i> 刷新日志
        </button>
        <button class="btn btn-outline-secondary me-2" onclick="toggleAutoRefresh()">
            <i class="bi bi-play-circle" id="auto-refresh-icon"></i>
            <span id="auto-refresh-text">开启自动刷新</span>
        </button>
        <button class="btn btn-outline-info" onclick="downloadLogs()">
            <i class="bi bi-download"></i> 下载日志
        </button>
    </div>
</div>

<!-- 日志过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <input type="text" class="form-control" id="search-input" placeholder="搜索日志内容..." onkeyup="filterLogs()">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="level-filter" onchange="filterLogs()">
                    <option value="">所有级别</option>
                    <option value="INFO">INFO</option>
                    <option value="WARNING">WARNING</option>
                    <option value="ERROR">ERROR</option>
                    <option value="DEBUG">DEBUG</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="lines-count" onchange="changeLinesCount()">
                    <option value="50">最近50行</option>
                    <option value="100">最近100行</option>
                    <option value="200" selected>最近200行</option>
                    <option value="500">最近500行</option>
                    <option value="1000">最近1000行</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                    <i class="bi bi-x-circle"></i> 重置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 日志统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <div class="metric-value text-primary" id="total-lines">0</div>
                <div class="metric-label">总行数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <div class="metric-value text-info" id="info-count">0</div>
                <div class="metric-label">INFO</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <div class="metric-value text-warning" id="warning-count">0</div>
                <div class="metric-label">WARNING</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <div class="metric-value text-danger" id="error-count">0</div>
                <div class="metric-label">ERROR</div>
            </div>
        </div>
    </div>
</div>

<!-- 日志内容 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-terminal"></i> 日志内容</h5>
        <div>
            <small class="text-muted">最后更新: <span id="last-update">-</span></small>
        </div>
    </div>
    <div class="card-body p-0">
        <div id="logs-container" class="log-container" style="max-height: 600px; padding: 1rem;">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载日志...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let allLogs = [];
let filteredLogs = [];
let autoRefreshInterval = null;
let autoRefreshEnabled = false;

// 加载日志
async function loadLogs(lines = 200) {
    const data = await apiCall(`/api/logs?lines=${lines}`);
    if (data && data.logs) {
        allLogs = data.logs;
        filteredLogs = [...allLogs];
        updateLogsDisplay();
        updateStatistics();
        updateLastUpdateTime();
    }
}

// 更新日志显示
function updateLogsDisplay() {
    const container = document.getElementById('logs-container');
    
    if (filteredLogs.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">没有找到匹配的日志</p>';
        return;
    }
    
    let html = '';
    filteredLogs.forEach((log, index) => {
        const logClass = getLogClass(log);
        const logIcon = getLogIcon(log);
        html += `<div class="log-line ${logClass}" data-index="${index}">
            <i class="bi ${logIcon} me-2"></i>${escapeHtml(log)}
        </div>`;
    });
    
    container.innerHTML = html;
    container.scrollTop = container.scrollHeight;
}

// 获取日志样式类
function getLogClass(log) {
    if (log.includes('ERROR')) return 'text-danger';
    if (log.includes('WARNING')) return 'text-warning';
    if (log.includes('INFO')) return 'text-info';
    if (log.includes('DEBUG')) return 'text-muted';
    return '';
}

// 获取日志图标
function getLogIcon(log) {
    if (log.includes('ERROR')) return 'bi-exclamation-triangle-fill';
    if (log.includes('WARNING')) return 'bi-exclamation-triangle';
    if (log.includes('INFO')) return 'bi-info-circle';
    if (log.includes('DEBUG')) return 'bi-bug';
    return 'bi-circle';
}

// 转义HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 更新统计信息
function updateStatistics() {
    const totalLines = allLogs.length;
    const infoCount = allLogs.filter(log => log.includes('INFO')).length;
    const warningCount = allLogs.filter(log => log.includes('WARNING')).length;
    const errorCount = allLogs.filter(log => log.includes('ERROR')).length;
    
    document.getElementById('total-lines').textContent = totalLines;
    document.getElementById('info-count').textContent = infoCount;
    document.getElementById('warning-count').textContent = warningCount;
    document.getElementById('error-count').textContent = errorCount;
}

// 更新最后更新时间
function updateLastUpdateTime() {
    document.getElementById('last-update').textContent = new Date().toLocaleTimeString('zh-CN');
}

// 过滤日志
function filterLogs() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    const levelFilter = document.getElementById('level-filter').value;
    
    filteredLogs = allLogs.filter(log => {
        let matchesSearch = true;
        let matchesLevel = true;
        
        if (searchTerm) {
            matchesSearch = log.toLowerCase().includes(searchTerm);
        }
        
        if (levelFilter) {
            matchesLevel = log.includes(levelFilter);
        }
        
        return matchesSearch && matchesLevel;
    });
    
    updateLogsDisplay();
}

// 重置过滤器
function resetFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('level-filter').value = '';
    filteredLogs = [...allLogs];
    updateLogsDisplay();
}

// 改变显示行数
function changeLinesCount() {
    const lines = document.getElementById('lines-count').value;
    loadLogs(parseInt(lines));
}

// 刷新日志
async function refreshLogs() {
    const lines = document.getElementById('lines-count').value;
    await loadLogs(parseInt(lines));
    showAlert('日志已刷新', 'success');
}

// 切换自动刷新
function toggleAutoRefresh() {
    const icon = document.getElementById('auto-refresh-icon');
    const text = document.getElementById('auto-refresh-text');
    
    if (autoRefreshEnabled) {
        // 停止自动刷新
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        autoRefreshEnabled = false;
        icon.className = 'bi bi-play-circle';
        text.textContent = '开启自动刷新';
        showAlert('自动刷新已停止', 'info');
    } else {
        // 开始自动刷新
        autoRefreshInterval = setInterval(() => {
            const lines = document.getElementById('lines-count').value;
            loadLogs(parseInt(lines));
        }, 5000); // 每5秒刷新一次
        autoRefreshEnabled = true;
        icon.className = 'bi bi-stop-circle';
        text.textContent = '停止自动刷新';
        showAlert('自动刷新已开启（每5秒）', 'success');
    }
}

// 下载日志
function downloadLogs() {
    const content = allLogs.join('\n');
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `alarm_monitor_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.log`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    showAlert('日志下载已开始', 'success');
}

// 页面加载完成后加载日志
document.addEventListener('DOMContentLoaded', function() {
    loadLogs();
});

// 页面卸载时清除定时器
window.addEventListener('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>

<style>
.log-line {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    padding: 2px 0;
    border-bottom: 1px solid #f0f0f0;
}

.log-line:hover {
    background-color: #f8f9fa;
}

.log-container {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    overflow-y: auto;
}
</style>
{% endblock %}
